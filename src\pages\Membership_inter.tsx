import React, { useEffect, useState } from 'react';
import { toast } from 'react-hot-toast';
import { createOrder, verifyPaypalPayment } from '../services/paymentService';
import { getCurrentUser } from '../services/userService';
import { useUser } from '../contexts/UserContext';
import { Modal } from '../components/Modal';
import LoginPrompt from '../components/LoginPrompt';
import { QRCodeSVG } from 'qrcode.react';
import { motion } from 'framer-motion';
import LandingBackground from '../components/LandingBackground';
import Footer from '../components/Footer';
import PayPalButton from '../components/PayPalButton';
import PayPalSubscriptionButton from '../components/PayPalSubscriptionButton';
import { useTranslation } from 'react-i18next';
import { isMobileDevice } from '../utils/deviceDetect';
import SEO from '../components/SEO';
import { useLanguageNavigate } from '../hooks/useLanguageNavigate';
import { INTERNAL_PAYPAL_CONFIG, INTERNAL_PRICING_USD, INTERNAL_PRICING_CNY } from '../config/pricingConfig';
import { checkPrivilegeStatus } from '../services/invitationService';
import { useTheme } from '../contexts/ThemeContext';
// import Link from 'next/link';

// 创建一个根据主题返回toast样式的辅助函数
const getToastStyle = (isDark: boolean) => ({
  padding: '16px 24px',
  borderRadius: '16px',
  background: isDark ? 'rgba(51, 65, 85, 0.95)' : 'rgba(255, 255, 255, 0.95)',
  border: isDark ? '1px solid rgba(148, 163, 184, 0.2)' : '1px solid rgba(168, 85, 247, 0.2)',
  color: isDark ? '#FFFFFF' : '#1F2937',
  maxWidth: '90%',
  width: '420px',
  margin: '0 auto',
  boxShadow: isDark ? '0 8px 32px rgba(0, 0, 0, 0.4)' : '0 8px 32px rgba(0, 0, 0, 0.1)',
  backdropFilter: 'blur(8px)',
  fontSize: '15px',
  lineHeight: '1.5',
  textAlign: 'center' as const,
  zIndex: 9999,
  fontFamily: 'var(--font-sans)',
});

// 创建一个确保内部文本为黑色的模态框组件
const BlackTextModal: React.FC<{
  open: boolean;
  onClose: () => void;
  children: React.ReactNode;
  className?: string;
}> = ({ open, onClose, children, className = '' }) => {
  if (!open) return null;
  
  return (
    <Modal open={open} onClose={onClose} className={className}>
      <div className="black-text-modal">
        <style>
          {`
            .black-text-modal,
            .black-text-modal div,
            .black-text-modal p,
            .black-text-modal span {
              color: #000000 !important;
            }
            .black-text-modal button:not(.bg-blue-600):not(.bg-purple-600):not([class*="text-white"]) {
              color: #000000 !important;
            }
            .black-text-modal button.bg-blue-600,
            .black-text-modal button.bg-purple-600,
            .black-text-modal button[class*="text-white"] span {
              color: #FFFFFF !important;
            }
          `}
        </style>
        {children}
      </div>
    </Modal>
  );
};

// 添加用于突出显示链接的FAQ内容组件
const EnhancedFaqContent: React.FC<{ htmlContent: string; isDark: boolean }> = ({ htmlContent, isDark }) => {
  // 创建包含HTML的DOM元素
  const createMarkup = () => {
    return { __html: htmlContent };
  };

  return (
    <div 
      className={`faq-content mt-2 sm:mt-3 pl-6 sm:pl-8 pr-4 text-sm sm:text-base ${isDark ? 'text-gray-400' : 'text-gray-600'}`}
      dangerouslySetInnerHTML={createMarkup()}
      style={{
        // 添加内联样式
        "--link-color": isDark ? "rgb(216, 180, 254)" : "rgb(126, 34, 206)",
        "--link-bg-color": isDark ? "rgba(216, 180, 254, 0.1)" : "rgba(126, 34, 206, 0.1)",
        "--link-bg-hover-color": isDark ? "rgba(216, 180, 254, 0.2)" : "rgba(126, 34, 206, 0.15)",
      } as React.CSSProperties}
    />
  );
};

// 添加全局CSS样式
const faqContentStyles = `
  .faq-content a {
    color: var(--link-color);
    font-weight: 600;
    text-decoration: none;
    position: relative;
    transition: all 0.2s ease;
    padding: 1px 4px;
    border-radius: 3px;
    background-color: var(--link-bg-color);
    box-shadow: 0 0 0 1px var(--link-color, transparent);
    margin: 0 1px;
    display: inline-block;
  }
  
  .faq-content a:hover {
    text-decoration: none;
    box-shadow: 0 0 0 1.5px var(--link-color, transparent);
    background-color: var(--link-bg-hover-color);
    transform: translateY(-1px);
  }
  
  .faq-content a:active {
    transform: translateY(0);
  }
`;

const CheckmarkAnimation: React.FC<{
  size?: number;
  color?: string;
  strokeWidth?: number;
  animationDuration?: number;
}> = ({
  size = 32,
  color = '#4CAF50',
  strokeWidth = 4,
}) => {
  const circleVariants = {
    hidden: { opacity: 0, scale: 0.5 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: { duration: 0.5, ease: "easeOut" }
    }
  };

  const checkVariants = {
    hidden: { pathLength: 0, opacity: 0 },
    visible: {
      pathLength: 1,
      opacity: 1,
      transition: {
        pathLength: { duration: 0.5, ease: "easeOut" },
        opacity: { duration: 0.01 },
      },
    },
  };

  return (
    <div style={{ width: size, height: size }}>
      <motion.svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 100 100"
        initial="hidden"
        animate="visible"
      >
        <motion.circle
          cx="50"
          cy="50"
          r="45"
          fill="none"
          stroke={color}
          strokeWidth={strokeWidth}
          variants={circleVariants}
        />
        <motion.path
          d="M30,50 L45,65 L70,40"
          fill="none"
          stroke={color}
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          strokeLinejoin="round"
          variants={checkVariants}
        />
      </motion.svg>
    </div>
  );
};

interface PlanType {
  price: number;
}

function Membership() {
  const { t } = useTranslation();
  const { navigate } = useLanguageNavigate();
  const { theme } = useTheme();
  const isDark = theme === 'dark';
  const [orderId, setOrderId] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<PlanType | null>(null);
  const [showSuccess, setShowSuccess] = useState(false);
  const [currentPlan, setCurrentPlan] = useState<'free' | 'monthly' | 'yearly'>('free');
  const [showLoginPrompt, setShowLoginPrompt] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const { user, setUser } = useUser();
  const [paymentMethod, setPaymentMethod] = useState<'alipay' | 'wechat' | 'paypal'>(() => {
    // 检查是否为移动设备
    const isMobile = isMobileDevice();
    // 如果是移动设备，默认使用支付宝，否则使用PayPal
    return isMobile ? 'alipay' : 'paypal';
  });
  const [currency, setCurrency] = useState<'CNY' | 'USD'>('USD');
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');
  const [pendingOrder, setPendingOrder] = useState<{amount: number; planName: string} | null>(null);
  const [useCount, setUseCount] = useState<number>(1);
  const checkOrderTimer = React.useRef<NodeJS.Timeout>();
  const [isVerifying, setIsVerifying] = useState(false);
  const [showPayPalButton, setShowPayPalButton] = useState(false);
  const [expandedFaqs, setExpandedFaqs] = useState<Record<string, boolean>>({
    payment: false,
    cancel: false,
    password: false,
    howToCancel: false,
    priceChange: false,
    changePlan: false
  });

  // 使用配置文件中的PayPal计划ID
  // 在生产环境中使用PRODUCTION配置，开发/测试环境使用SANDBOX配置
  const MONTHLY_PLAN_ID = import.meta.env.PROD 
    ? INTERNAL_PAYPAL_CONFIG.PRODUCTION.MONTHLY_PLAN_ID 
    : INTERNAL_PAYPAL_CONFIG.SANDBOX.MONTHLY_PLAN_ID;
    
  const YEARLY_PLAN_ID = import.meta.env.PROD
    ? INTERNAL_PAYPAL_CONFIG.PRODUCTION.YEARLY_PLAN_ID
    : INTERNAL_PAYPAL_CONFIG.SANDBOX.YEARLY_PLAN_ID;

  // 检查用户是否有内部折扣权限并重定向
  useEffect(() => {
    const checkInternalDiscount = async () => {
      try {
        if (!user) {
          // 未登录用户重定向到普通会员页面
          navigate('/membership');
          return;
        }

        const privilegeStatus = await checkPrivilegeStatus();
        
        // 判断用户是否有内部折扣权限且未使用过折扣
        if (!privilegeStatus.hasInternalPrivilege || 
            !privilegeStatus.privilegeInfo || 
            privilegeStatus.privilegeInfo.has_used_discount) {
          // 用户没有内部折扣权限或已使用过折扣，重定向到普通会员页面
          navigate('/membership');
          return;
        }
      } catch (error) {
        // console.error('检查内部折扣权限失败:', error);
        // 发生错误时也重定向到普通会员页面
        navigate('/membership');
      }
    };
    
    checkInternalDiscount();
  }, [user, navigate]);

  useEffect(() => {
    setSelectedPlan({ price: INTERNAL_PRICING_CNY.MONTHLY.PRICE });
  }, []);

  const toggleFaq = (key: string) => {
    setExpandedFaqs(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  useEffect(() => {
    if (user?.vipStatus === 'active') {
      setCurrentPlan(user.vipType === 'yearly' ? 'yearly' : 'monthly');
    } else {
      setCurrentPlan('free');
    }
  }, [user]);

  useEffect(() => {
    if (orderId && paymentMethod === 'wechat') {
      (async () => {
        try {
          const response = await fetch(`${import.meta.env.VITE_API_URL}/api/payment/order-status/${orderId}?paymentMethod=wechat`, {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
          });
          
          if (!response.ok) {
            throw new Error(t('membership.errors.order_status_failed'));
          }
          
          const result = await response.json();
          
          if (result.data.tradeState === 'SUCCESS') {
            setShowSuccess(true);
            setTimeout(async () => {
              try {
                const updatedUser = await getCurrentUser();
                setUser(updatedUser);
                setCurrentPlan(updatedUser.vipStatus === 'yearly' ? 'yearly' : 'monthly');
                
                // 根据购买类型显示不同的成功提示
                const successMessage = (pendingOrder?.planName === 'pay_per_use' || !pendingOrder?.planName)
                  ? t('membership.success.pay_per_use_activated')
                  : t('membership.success.member_activated');
                
                toast.success(successMessage, {
                  style: getToastStyle(isDark)
                });
                
                setQrCodeUrl('');
                setOrderId('');
                setShowSuccess(false);
                handleClosePayment();
              } catch (error) {
                // console.error('更新用户信息失败:', error);
                toast.error(t('membership.errors.status_update_failed'));
              }
            }, 1000);
          }
        } catch (error) {
          // console.error('检查订单状态失败:', error);
        }
      })();
      
      checkOrderTimer.current = setInterval(async () => {
        try {
          const response = await fetch(`${import.meta.env.VITE_API_URL}/api/payment/order-status/${orderId}?paymentMethod=wechat`, {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
          });
          
          if (!response.ok) {
            throw new Error(t('membership.errors.order_status_failed'));
          }
          
          const result = await response.json();
          
          if (result.data.tradeState === 'SUCCESS') {
            if (checkOrderTimer.current) {
              clearInterval(checkOrderTimer.current);
            }
            
            setShowSuccess(true);
            
            setTimeout(async () => {
              try {
                const updatedUser = await getCurrentUser();
                setUser(updatedUser);
                setCurrentPlan(updatedUser.vipStatus === 'yearly' ? 'yearly' : 'monthly');
                
                // 根据购买类型显示不同的成功提示
                const successMessage = (pendingOrder?.planName === 'pay_per_use' || !pendingOrder?.planName)
                  ? t('membership.success.pay_per_use_activated')
                  : t('membership.success.member_activated');
                
                toast.success(successMessage, {
                  style: getToastStyle(isDark)
                });
                
                setQrCodeUrl('');
                setOrderId('');
                setShowSuccess(false);
                handleClosePayment();
              } catch (error) {
                // console.error('更新用户信息失败:', error);
                toast.error(t('membership.errors.status_update_failed'));
              }
            }, 1000);
          }
        } catch (error) {
          // console.error('检查订单状态失败:', error);
        }
      }, 3000);
    }
    
    return () => {
      if (checkOrderTimer.current) {
        clearInterval(checkOrderTimer.current);
      }
    };
  }, [orderId, paymentMethod]);

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const outTradeNo = urlParams.get('out_trade_no');
    
    if (outTradeNo) {
      setQrCodeUrl('');
      setOrderId(outTradeNo);
      setPaymentMethod('alipay');
      
      window.history.replaceState({}, '', window.location.pathname);

      (async () => {
        try {
          const response = await fetch(`${import.meta.env.VITE_API_URL}/api/payment/order-status/${outTradeNo}?paymentMethod=alipay`, {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
          });
          
          if (!response.ok) {
            throw new Error(t('membership.errors.order_status_failed'));
          }
          
          const result = await response.json();
          
          if (result.data.tradeState === 'SUCCESS') {
            setShowSuccess(true);
            setTimeout(async () => {
              try {
                const updatedUser = await getCurrentUser();
                setUser(updatedUser);
                setCurrentPlan(updatedUser.vipStatus === 'yearly' ? 'yearly' : 'monthly');
                
                // 根据购买类型显示不同的成功提示
                const successMessage = (pendingOrder?.planName === 'pay_per_use' || !pendingOrder?.planName)
                  ? t('membership.success.pay_per_use_activated')
                  : t('membership.success.member_activated');
                
                toast.success(successMessage, {
                  style: getToastStyle(isDark)
                });
                
                setOrderId('');
                setShowSuccess(false);
              } catch (error) {
                // console.error('更新用户信息失败:', error);
                toast.error(t('membership.errors.status_update_failed'));
              }
            }, 1000);
          }
        } catch (error) {
          // console.error('检查支付宝订单状态失败:', error);
        }
      })();
    }
  }, []);

  useEffect(() => {
    document.body.style.overflow = 'hidden';
    return () => {
      document.body.style.overflow = 'auto';
    };
  }, []);

  const handleSubscribe = async (amount: number, planName: string) => {
    if (!user) {
      setShowLoginPrompt(true);
      return;
    }

    setLoading(false);
    setIsVerifying(false);
    setOrderId('');
    setQrCodeUrl('');
    setShowPayPalButton(false);
    setShowSuccess(false);
    document.body.style.cursor = 'default';
    
    const paypalContainer = document.querySelector('.paypal-button-container');
    if (paypalContainer) {
        paypalContainer.innerHTML = '';
    }
    const paypalScript = document.querySelector('script[src*="paypal.com/sdk"]');
    if (paypalScript) {
        paypalScript.remove();
    }
    
    setPendingOrder({ amount, planName });
    
    // 根据当前货币选择正确的价格配置
    const pricingConfig = currency === 'CNY' ? INTERNAL_PRICING_CNY : INTERNAL_PRICING_USD;
    
    let actualAmount = amount;
    if (planName === 'monthly') {
      actualAmount = pricingConfig.MONTHLY.PRICE;
    } else if (planName === 'yearly') {
      actualAmount = pricingConfig.YEARLY.PRICE;
    } else {
      actualAmount = useCount * pricingConfig.PAY_PER_USE.PRICE_PER_USE;
    }
    setSelectedPlan({ price: actualAmount });
    
    // 先设置支付模态框的显示状态
    setShowPaymentModal(true);
    
    if (currency === 'USD') {
      setPaymentMethod('paypal');
      // 延迟显示PayPal按钮，确保容器已经渲染
      setTimeout(() => {
        setShowPayPalButton(true);
      }, 1000);
    }
  };

  // 监听货币变化，更新按次付费价格
  useEffect(() => {
    if (pendingOrder?.planName === 'pay_per_use' && useCount > 0) {
      const pricePerUse = currency === 'CNY' 
        ? INTERNAL_PRICING_CNY.PAY_PER_USE.PRICE_PER_USE 
        : INTERNAL_PRICING_USD.PAY_PER_USE.PRICE_PER_USE;
      setSelectedPlan({ price: useCount * pricePerUse });
    }
  }, [currency, useCount, pendingOrder?.planName]);

  const handleClosePayment = () => {
    resetPaymentStates();
  };

  const handlePaymentMethodChange = (method: 'alipay' | 'wechat' | 'paypal') => {
    setPaymentMethod(method);
    setQrCodeUrl('');
    setOrderId('');
    setShowPayPalButton(false);
    
    if (method === 'paypal') {
        const paypalContainer = document.querySelector('.paypal-button-container');
        if (paypalContainer) {
            paypalContainer.innerHTML = '';
        }
        const paypalScript = document.querySelector('script[src*="paypal.com/sdk"]');
        if (paypalScript) {
            paypalScript.remove();
        }
        setTimeout(() => {
            setShowPayPalButton(true);
        }, 100);
        setShowPaymentModal(false);
    }
  };

  const resetPaymentStates = () => {
    setLoading(false);
    setIsVerifying(false);
    setShowPaymentModal(false);
    setPendingOrder(null);
    setSelectedPlan(null);
    setOrderId('');
    setQrCodeUrl('');
    setShowPayPalButton(false);
    setShowSuccess(false);
    document.body.style.overflow = 'auto';
    document.body.style.cursor = 'default';
    
    const paypalContainer = document.querySelector('.paypal-button-container');
    if (paypalContainer) {
        paypalContainer.innerHTML = '';
    }
    
    const paypalScript = document.querySelector('script[src*="paypal.com/sdk"]');
    if (paypalScript) {
        paypalScript.remove();
    }
  };

  useEffect(() => {
    if (paymentMethod === 'paypal' && !orderId && !loading) {
      const timer = setTimeout(() => {
        setShowPayPalButton(true);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [paymentMethod, orderId, loading]);

  const handleCreateOrder = async () => {
    if (!pendingOrder) return;
    
    try {
      setLoading(true);
      
      if (paymentMethod === 'paypal') {
        setLoading(false);
        return;
      }

      // 使用新的设备检测函数
      const isMobile = isMobileDevice();
      
      // 如果是移动设备且选择了微信支付，显示提示弹窗并阻止继续支付流程
      if (paymentMethod === 'wechat' && isMobile) {
        toast(t('membership.toast.wechat_mobile_unsupported'), {
          icon: '⚠️',
          duration: 5000,
          style: getToastStyle(isDark),
        });
        setLoading(false);
        return;
      }

      // 根据当前货币选择正确的价格配置
      const pricingConfig = currency === 'CNY' ? INTERNAL_PRICING_CNY : INTERNAL_PRICING_USD;

      let actualAmount;
      if (pendingOrder.planName === 'monthly') {
        actualAmount = pricingConfig.MONTHLY.PRICE;
      } else if (pendingOrder.planName === 'yearly') {
        actualAmount = pricingConfig.YEARLY.PRICE;
      } else {
        actualAmount = useCount * pricingConfig.PAY_PER_USE.PRICE_PER_USE;
      }

      const response = await createOrder({
        amount: actualAmount,
        productId: pendingOrder.planName,
        productName: pendingOrder.planName === 'pay_per_use' 
          ? `塔罗会员-按次付费-${useCount}(内部折扣)` 
          : `塔罗会员-${pendingOrder.planName}(内部折扣)`,
        userId: user!.id,
        currency: currency, // 使用当前选择的货币
        paymentMethod
      });

      if (paymentMethod === 'alipay') {
        if (!response.data.payUrl) {
          throw new Error(t('membership.errors.no_alipay_link'));
        }
        
        // 使用统一的设备检测函数
        const isMobile = isMobileDevice();
        
        // 根据设备类型决定如何打开支付链接
        if (isMobile) {
          // 移动设备直接在当前窗口打开
          window.location.href = response.data.payUrl;
        } else {
          // PC端使用新窗口打开
          const newWindow = window.open('', '_blank');
          if (newWindow) {
            newWindow.location.href = response.data.payUrl;
          } else {
            // 如果弹窗被阻止，则在新标签页打开
            window.open(response.data.payUrl, '_blank');
          }
        }

        // 显示支付确认提示
        toast(t('membership.toast.alipay_new_window'), {
          icon: '💳',
          duration: 5000,
          style: getToastStyle(isDark),
        });
      } else {
        if (!response.data.qrCodeUrl) {
          throw new Error(t('membership.errors.no_wechat_qrcode'));
        }
        setQrCodeUrl(response.data.qrCodeUrl);
      }
      setOrderId(response.data.orderId);
    } catch (error) {
      //    console.error('创建订单失败:', error);
      toast.error(t('membership.errors.create_order_failed'));
      handleClosePayment();
    } finally {
      setLoading(false);
    }
  };

  const handlePaymentConfirmation = async () => {
    try {
      setLoading(true);
      const response = await fetch(`${import.meta.env.VITE_API_URL}/api/payment/order-status/${orderId}?paymentMethod=alipay`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (!response.ok) {
        throw new Error(t('membership.errors.order_status_failed'));
      }
      
      const result = await response.json();
      
      if (result.data.tradeState === 'TRADE_SUCCESS') {
        setShowSuccess(true);
        setTimeout(async () => {
          try {
            const updatedUser = await getCurrentUser();
            setUser(updatedUser);
            setCurrentPlan(updatedUser.vipStatus === 'yearly' ? 'yearly' : 'monthly');
            
            // 根据购买类型显示不同的成功提示
            const successMessage = (pendingOrder?.planName === 'pay_per_use' || !pendingOrder?.planName)
              ? t('membership.success.pay_per_use_activated')
              : t('membership.success.member_activated');
            
            toast.success(successMessage, {
              style: getToastStyle(isDark)
            });
            
            setQrCodeUrl('');
            setOrderId('');
            setShowSuccess(false);
            handleClosePayment();
          } catch (error) {
            // console.error('更新用户信息失败:', error);
            toast.error(t('membership.errors.status_update_failed'));
          }
        }, 1000);
      } else {
        toast(t('membership.toast.payment_incomplete'), {
          icon: '❌',
          duration: 5000,
          style: getToastStyle(isDark),
        });
      }
    } catch (error) {
      // console.error('检查订单状态失败:', error);
      toast.error(t('membership.errors.order_status_failed'));
    } finally {
      setLoading(false);
    }
  };

  const QRCodeWithAnimation: React.FC<{ url: string; showSuccess: boolean }> = ({ url, showSuccess }) => {
    return (
      <div className="relative w-[200px] h-[200px]">
        <motion.div
          animate={{ opacity: showSuccess ? 0 : 1 }}
          transition={{ duration: 0.3 }}
        >
          <QRCodeSVG value={url} size={200} />
        </motion.div>
        {showSuccess && (
          <motion.div
            className="absolute inset-0 flex items-center justify-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.3 }}
          >
            <CheckmarkAnimation size={160} color="#4CAF50" strokeWidth={6} />
          </motion.div>
        )}
      </div>
    );
  };

  const LoadingSpinner: React.FC = () => (
    <div className="flex justify-center items-center">
      <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500"></div>
    </div>
  );

  const handlePayPalPayment = async (details: any) => {
    try {
      setIsVerifying(true);
      setLoading(true);

      // 根据当前货币选择正确的价格和价格配置
      const pricingConfig = currency === 'CNY' ? INTERNAL_PRICING_CNY : INTERNAL_PRICING_USD;
      
      const orderParams = {
        amount: pendingOrder?.planName === 'monthly'
          ? pricingConfig.MONTHLY.PRICE
          : pendingOrder?.planName === 'yearly'
            ? pricingConfig.YEARLY.PRICE
            : Number((useCount * pricingConfig.PAY_PER_USE.PRICE_PER_USE).toFixed(2)),
        productId: pendingOrder?.planName || 'pay_per_use',
        productName: (pendingOrder?.planName === 'pay_per_use' || !pendingOrder?.planName)
          ? `塔罗会员-按次付费-${useCount}(内部折扣)`
          : `塔罗会员-${pendingOrder?.planName}(内部折扣)`,
        userId: user!.id,
        currency: currency, // 使用当前选择的货币
        paymentMethod: 'paypal' as const,
        paypalOrderId: details.id
      };

      const orderResponse = await createOrder(orderParams);

      if (!orderResponse.success) {
        throw new Error(t('membership.errors.order_creation_failed'));
      }

      setOrderId(orderResponse.data.orderId);

      const verifyParams = {
        orderId: orderResponse.data.orderId,
        paypalOrderId: details.id,
        paypalPaymentId: details.purchase_units[0].payments.captures[0].id
      };

      const verifyResult = await verifyPaypalPayment(verifyParams);

      if (verifyResult.success) {
        const updatedUser = await getCurrentUser();
        setUser(updatedUser);
        setCurrentPlan(updatedUser.vipStatus === 'yearly' ? 'yearly' : 'monthly');
        
        // 根据购买类型显示不同的成功提示
        const successMessage = (pendingOrder?.planName === 'pay_per_use' || !pendingOrder?.planName)
          ? t('membership.success.pay_per_use_activated')
          : t('membership.success.member_activated');
        
        toast.success(successMessage, {
          style: getToastStyle(isDark)
        });
        
        setShowSuccess(true);
        
        setTimeout(() => {
          setShowPayPalButton(false);
          setPendingOrder(null);
          setShowPaymentModal(false);
          setOrderId('');
          setQrCodeUrl('');
          document.body.style.cursor = 'default';
          
          const paypalContainer = document.querySelector('.paypal-button-container');
          if (paypalContainer) {
            paypalContainer.innerHTML = '';
          }
          
          const paypalScript = document.querySelector('script[src*="paypal.com/sdk"]');
          if (paypalScript) {
            paypalScript.remove();
          }
          
          setIsVerifying(false);
          setShowSuccess(false);
          setLoading(false);
        }, 1000);
      } else {
        throw new Error(verifyResult.message || '支付验证失败');
      }
    } catch (error: any) {
      // console.error('PayPal支付处理错误:', error);
      toast.error(error.message || '支付验证失败，请联系客服');
      setIsVerifying(false);
      setLoading(false);
      resetPaymentStates();
    }
  };

  const handlePayPalSubscription = async (data: any) => {
    try {
      setIsVerifying(true);
      setLoading(true);

      // 根据当前货币选择正确的价格配置
      const pricingConfig = currency === 'CNY' ? INTERNAL_PRICING_CNY : INTERNAL_PRICING_USD;

      // 创建订阅订单
      const orderParams = {
        amount: pendingOrder?.planName === 'yearly' 
          ? pricingConfig.YEARLY.PRICE 
          : pricingConfig.MONTHLY.PRICE,
        productId: pendingOrder?.planName || 'monthly',
        productName: `塔罗会员-${pendingOrder?.planName || 'monthly'}(内部折扣)`,
        userId: user!.id,
        currency: currency, // 使用当前选择的货币
        paymentMethod: 'paypal' as const,
        subscriptionId: data.subscriptionID,
        isSubscription: true
      };

      // 创建订单记录
      const orderResponse = await createOrder(orderParams);

      if (!orderResponse.success) {
        throw new Error(t('membership.errors.order_creation_failed'));
      }

      // 验证订阅支付
      const verifyParams = {
        orderId: orderResponse.data.orderId,
        subscriptionId: data.subscriptionID,
        isSubscription: true
      };

      const verifyResult = await verifyPaypalPayment(verifyParams);

      if (verifyResult.success) {
        const updatedUser = await getCurrentUser();
        setUser(updatedUser);
        setCurrentPlan(updatedUser.vipType === 'yearly' ? 'yearly' : 'monthly');
        
        // 根据购买类型显示不同的成功提示
        const successMessage = (pendingOrder?.planName === 'pay_per_use' || !pendingOrder?.planName)
          ? t('membership.success.pay_per_use_activated')
          : t('membership.success.member_activated');
        
        toast.success(successMessage, {
          style: getToastStyle(isDark)
        });
        
        setShowSuccess(true);
        
        setTimeout(() => {
          setShowPayPalButton(false);
          setPendingOrder(null);
          setShowPaymentModal(false);
          setOrderId('');
          setQrCodeUrl('');
          document.body.style.cursor = 'default';
          
          const paypalContainer = document.querySelector('.paypal-button-container');
          if (paypalContainer) {
            paypalContainer.innerHTML = '';
          }
          
          const paypalScript = document.querySelector('script[src*="paypal.com/sdk"]');
          if (paypalScript) {
            paypalScript.remove();
          }
          
          setIsVerifying(false);
          setShowSuccess(false);
          setLoading(false);
        }, 1000);
      } else {
        throw new Error(verifyResult.message || '订阅验证失败');
      }
    } catch (error: any) {
      // console.error('PayPal订阅处理错误:', error);
      toast.error(error.message || '订阅验证失败，请联系客服');
      setIsVerifying(false);
      setLoading(false);
      resetPaymentStates();
    }
  };

  const renderPaymentMethod = () => {
    if (paymentMethod === 'paypal' && showPayPalButton) {
      try {
        if (pendingOrder?.planName === 'monthly' || pendingOrder?.planName === 'yearly') {
          return (
            <div className="paypal-button-container">
              <PayPalSubscriptionButton
                planId={pendingOrder.planName === 'monthly' ? MONTHLY_PLAN_ID : YEARLY_PLAN_ID}
                onSuccess={handlePayPalSubscription}
                onError={() => {
                  // console.error('PayPal error:', error);
                  toast(t('membership.errors.payment_failed'), {
                    icon: '❌',
                    duration: 5000,
                    style: getToastStyle(isDark),
                  });
                  resetPaymentStates();
                }}
                onCancel={() => {
                  toast(t('membership.errors.payment_cancelled'), {
                    icon: '⚠️',
                    duration: 5000,
                    style: getToastStyle(isDark),
                  });
                  handleClosePayment();
                }}
              />
            </div>
          );
        } else {
          return (
            <div className="paypal-button-container">
              <PayPalButton
                amount={pendingOrder?.planName === 'pay_per_use' 
                  ? Number((useCount * INTERNAL_PRICING_USD.PAY_PER_USE.PRICE_PER_USE).toFixed(2)) 
                  : pendingOrder?.amount || 0}
                currency="USD"
                onSuccess={handlePayPalPayment}
                onError={() => {
                  // console.error('PayPal error:', error);
                  toast(t('membership.errors.payment_failed'), {
                    icon: '❌',
                    duration: 5000,
                    style: getToastStyle(isDark),
                  });
                  resetPaymentStates();
                }}
                onCancel={() => {
                  toast(t('membership.errors.payment_cancelled'), {
                    icon: '⚠️',
                    duration: 5000,
                    style: getToastStyle(isDark),
                  });
                  handleClosePayment();
                }}
              />
            </div>
          );
        }
      } catch (error) {
        // console.error('PayPal rendering error:', error);
        toast.error(t('membership.errors.payment_failed'));
        // 发生错误时重置状态
        resetPaymentStates();
        return null;
      }
    } else if (paymentMethod === 'alipay' || paymentMethod === 'wechat') {
      return (
        <QRCodeWithAnimation url={qrCodeUrl} showSuccess={showSuccess} />
      );
    }
    return null;
  };

  // 添加一个函数来判断按钮是否应该禁用
  const isSubscriptionDisabled = (planType: 'monthly' | 'yearly') => {
    if (!user) return false;
    if (currentPlan === 'monthly' && planType === 'yearly') return true;
    if (currentPlan === 'yearly' && planType === 'monthly') return true;
    return currentPlan === planType;
  };

  // 修改按钮文本获取函数
  const getSubscriptionButtonText = (planType: 'monthly' | 'yearly') => {
    if (!user) return t('membership.cards.button.subscribe');
    if (currentPlan === planType) return t('membership.cards.button.current_plan');
    return loading ? t('membership.cards.button.processing') : t('membership.cards.button.subscribe');
  };

  // 添加获取字体样式的函数
  const getFontClass = () => {
    const { i18n } = useTranslation();
    switch (i18n.language) {
      case 'ja':
        return 'font-sans japanese';
      case 'zh-CN':
      case 'zh-TW':
        return 'font-sans chinese';
      default:
        return 'font-sans';
    }
  };

  return (
    <div className="relative min-h-screen">
      {/* 添加全局样式 */}
      <style dangerouslySetInnerHTML={{ __html: faqContentStyles }} />
      <SEO 
      />
      <LandingBackground />
      <div className="flex-grow relative z-10">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 pb-8">
          <div className="text-center mt-8 sm:mt-10 mb-4 sm:mb-6">
            <h1 className={`main-title mb-1 ${getFontClass()}`}>{t('membership.title')}</h1>
            <p className={`sub-title ${getFontClass()}`}>{t('membership.subtitle')}</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className={`relative ${isDark ? 'bg-black/40' : 'bg-white/80'} backdrop-blur-xl rounded-2xl shadow-2xl border-2 ${isDark ? 'border-purple-500/20' : 'border-purple-300/30'} overflow-hidden hover:border-blue-400 transition-colors duration-300`}>
              <div className="absolute -top-32 -right-32 w-64 h-64 bg-purple-500/10 rounded-full blur-3xl"></div>
              <div className="absolute -bottom-32 -left-32 w-64 h-64 bg-pink-500/10 rounded-full blur-3xl"></div>

              <div className="relative p-8 h-full flex flex-col">
                <div className="flex-1">
                  <h3 className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400 mb-2">{t('membership.cards.basic.title')}</h3>
                  <p className={`${isDark ? 'text-gray-400' : 'text-gray-600'} mb-6`}>{t('membership.cards.basic.description')}</p>
                  <div className={`text-4xl font-bold ${isDark ? 'text-white' : 'text-gray-800'} mb-8`}>
                    {t('membership.cards.basic.price')}
                  </div>
                  <ul className={`space-y-4 ${isDark ? 'text-gray-300' : 'text-gray-600'} text-base sm:text-lg`}>
                    <li className="flex items-center">
                      <span className="text-purple-400 mr-2">✓</span>
                      {t('membership.cards.basic.features.free_readings')}
                    </li>
                    <li className="flex items-center">
                      <span className="text-purple-400 mr-2">✓</span>
                      {t('membership.cards.basic.features.daily_fortune')}
                    </li>
                    <li className="flex items-center">
                      <span className="text-purple-400 mr-2">✓</span>
                      {t('membership.cards.basic.features.free_card_back')}
                    </li>
                    <li className="flex items-center">
                      <span className="text-purple-400 mr-2">✓</span>
                      {t('membership.cards.basic.features.free_reader')}
                    </li>
                    <li className="flex items-center">
                      <span className="text-purple-400 mr-2">✓</span>
                      {t('membership.cards.basic.features.free_voice_style')}
                    </li>
                    <li className="flex items-center">
                      <span className="text-purple-400 mr-2">✓</span>
                      {t('membership.cards.basic.features.save_history')}
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <div className={`relative ${isDark ? 'bg-black/40' : 'bg-white/80'} backdrop-blur-xl rounded-2xl shadow-2xl border-2 ${isDark ? 'border-purple-500/20' : 'border-purple-300/30'} overflow-hidden hover:border-blue-400 transition-colors duration-300`}>
              <div className="absolute -top-32 -right-32 w-64 h-64 bg-purple-500/20 rounded-full blur-3xl"></div>
              <div className="absolute -bottom-32 -left-32 w-64 h-64 bg-pink-500/20 rounded-full blur-3xl"></div>

              <div className="absolute top-4 right-4">
                <span className="bg-gradient-to-r from-purple-500 to-pink-500 text-white text-sm px-3 py-1 rounded-full">
                  {t('membership.cards.monthly.badge')}
                </span>
              </div>

              <div className="relative p-8 h-full flex flex-col">
                <div className="flex-1">
                  <h3 className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400 mb-2">{t('membership.cards.monthly.title')}</h3>
                  <p className={`${isDark ? 'text-gray-400' : 'text-gray-600'} mb-6`}>{t('membership.cards.monthly.description')}</p>
                  <div className={`text-4xl font-bold ${isDark ? 'text-white' : 'text-gray-800'} mb-8`}>
                    US$<span className="font-sans">{INTERNAL_PRICING_USD.MONTHLY.DISPLAY_PRICE}</span> <span className={`text-lg font-normal ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>{t('membership.cards.monthly.price.period')}</span>
                    <div className="flex items-center mt-2">
                      <span className="text-base line-through text-gray-500 mr-2">US${INTERNAL_PRICING_USD.MONTHLY.ORIGINAL_PRICE}</span>
                      <span className="text-sm font-medium px-2 py-0.5 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-full">{INTERNAL_PRICING_USD.MONTHLY.DISCOUNT_PERCENT}% OFF</span>
                    </div>
                    </div>
                  <ul className={`space-y-4 ${isDark ? 'text-gray-300' : 'text-gray-600'} text-base sm:text-lg`}>
                    <li className="flex items-center">
                      <span className="text-purple-400 mr-2">✓</span>
                      {t('card_back_settings.vip_prompt.benefits.unlimited_readings')}
                    </li>
                    <li className="flex items-center">
                      <span className="text-purple-400 mr-2">✓</span>
                      {t('card_back_settings.vip_prompt.benefits.yearly_readings')}
                    </li>
                    <li className="flex items-center">
                      <span className="text-purple-400 mr-2">✓</span>
                      {t('card_back_settings.vip_prompt.benefits.yes_no_readings')}
                    </li>
                    <li className="flex items-center">
                      <span className="text-purple-400 mr-2">✓</span>
                      {t('card_back_settings.vip_prompt.benefits.all_readers')}
                    </li>
                    <li className="flex items-center">
                      <span className="text-purple-400 mr-2">✓</span>
                      {t('card_back_settings.vip_prompt.benefits.voice_history')}
                    </li>
                    <li className="flex items-center">
                      <span className="text-purple-400 mr-2">✓</span>
                      {t('card_back_settings.vip_prompt.benefits.card_backs')}
                    </li>
                    <li className="flex items-center">
                      <span className="text-purple-400 mr-2">✓</span>
                      {t('card_back_settings.vip_prompt.benefits.cancel_anytime')}
                    </li>
                  </ul>
                </div>
                <button
                  onClick={() => handleSubscribe(INTERNAL_PRICING_USD.MONTHLY.PRICE, 'monthly')}
                  disabled={loading || isSubscriptionDisabled('monthly')}
                  className={`w-full mt-8 px-6 py-3 bg-purple-600 hover:bg-purple-500 active:bg-purple-700 text-white rounded-xl transition-colors ${loading || (isSubscriptionDisabled('monthly') && currentPlan !== 'monthly') ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  <span className="text-white font-medium" style={{ color: '#ffffff' }}>
                    {getSubscriptionButtonText('monthly')}
                  </span>
                </button>
              </div>
            </div>

            <div className={`relative ${isDark ? 'bg-black/40' : 'bg-white/80'} backdrop-blur-xl rounded-2xl shadow-2xl border-2 ${isDark ? 'border-purple-500/20' : 'border-purple-300/30'} overflow-hidden hover:border-blue-400 transition-colors duration-300`}>
              <div className="absolute -top-32 -right-32 w-64 h-64 bg-purple-500/10 rounded-full blur-3xl"></div>
              <div className="absolute -bottom-32 -left-32 w-64 h-64 bg-pink-500/10 rounded-full blur-3xl"></div>

              <div className="absolute top-4 right-4">
                <span className="bg-gradient-to-r from-purple-500 to-pink-500 text-white text-sm px-3 py-1 rounded-full">
                  {t('membership.cards.yearly.badge')}
                </span>
              </div>

              <div className="relative p-8 h-full flex flex-col">
                <div className="flex-1">
                  <h3 className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400 mb-2">{t('membership.cards.yearly.title')}</h3>
                  <p className={`${isDark ? 'text-gray-400' : 'text-gray-600'} mb-6`}>{t('membership.cards.yearly.description')}</p>
                  <div className={`text-4xl font-bold ${isDark ? 'text-white' : 'text-gray-800'} mb-8`}>
                    US$<span className="font-sans">{INTERNAL_PRICING_USD.YEARLY.DISPLAY_PRICE}</span> <span className={`text-lg font-normal ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>{t('membership.cards.yearly.price.period')}</span>
                    <div className="flex items-center mt-2">
                      <span className="text-base line-through text-gray-500 mr-2">US${INTERNAL_PRICING_USD.YEARLY.ORIGINAL_PRICE}</span>
                      <span className="text-sm font-medium px-2 py-0.5 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-full">{INTERNAL_PRICING_USD.YEARLY.DISCOUNT_PERCENT}% OFF</span>
                    </div>
                    <div className={`text-sm font-normal ${isDark ? 'text-gray-400' : 'text-gray-500'} mt-1`}>
                      {t('membership.cards.yearly.price.monthly_discount')}
                    </div>
                  </div>
                  <ul className={`space-y-4 ${isDark ? 'text-gray-300' : 'text-gray-600'} text-base sm:text-lg`}>
                    {/* <li className="flex items-center">
                      <span className="text-purple-400 mr-2">✓</span>
                      {t('membership.cards.yearly.features.unlimited_deep_reading')}
                    </li> */}
                    <li className="flex items-center">
                      <span className="text-purple-400 mr-2">✓</span>
                      {t('membership.cards.yearly.features.all_monthly_features')}
                    </li>
                    <li className="flex items-center">
                      <span className="text-purple-400 mr-2">✓</span>
                      {t('membership.cards.yearly.features.save_25')}
                    </li>

                    <li className="flex items-center">
                      <span className="text-purple-400 mr-2">✓</span>
                      {t('membership.cards.yearly.features.cancel_anytime')}
                    </li>
                  </ul>
                </div>
                <button
                  onClick={() => handleSubscribe(INTERNAL_PRICING_USD.YEARLY.PRICE, 'yearly')}
                  disabled={loading || isSubscriptionDisabled('yearly')}
                  className={`w-full mt-8 px-6 py-3 bg-purple-600 hover:bg-purple-500 active:bg-purple-700 text-white rounded-xl transition-colors ${loading || (isSubscriptionDisabled('yearly') && currentPlan !== 'yearly') ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  <span className="text-white font-medium" style={{ color: '#ffffff' }}>
                    {getSubscriptionButtonText('yearly')}
                  </span>
                </button>
              </div>
            </div>

            <div className={`relative ${isDark ? 'bg-black/40' : 'bg-white/80'} backdrop-blur-xl rounded-2xl shadow-2xl border-2 ${isDark ? 'border-purple-500/20' : 'border-purple-300/30'} overflow-hidden hover:border-blue-400 transition-colors duration-300`}>
              <div className="absolute -top-32 -right-32 w-64 h-64 bg-purple-500/10 rounded-full blur-3xl"></div>
              <div className="absolute -bottom-32 -left-32 w-64 h-64 bg-pink-500/10 rounded-full blur-3xl"></div>

              <div className="relative p-8 h-full flex flex-col">
                <div className="flex-1">
                  <h3 className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400 mb-2">{t('membership.cards.pay_per_use.title')}</h3>
                  <p className={`${isDark ? 'text-gray-400' : 'text-gray-600'} mb-6`}>{t('membership.cards.pay_per_use.description')}</p>
                  <div className={`text-4xl font-bold ${isDark ? 'text-white' : 'text-gray-800'} mb-8`}>
                    US$<span className="font-sans">{(useCount * INTERNAL_PRICING_USD.PAY_PER_USE.PRICE_PER_USE).toFixed(2)}</span> <span className={`text-lg font-normal ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>{t('membership.cards.pay_per_use.total_price')}</span>
                    <div className="flex items-center mt-2">
                      <span className="text-base line-through text-gray-500 mr-2">US${(useCount * INTERNAL_PRICING_USD.PAY_PER_USE.ORIGINAL_PRICE_PER_USE).toFixed(2)}</span>
                      <span className="text-sm font-medium px-2 py-0.5 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-full">{INTERNAL_PRICING_USD.PAY_PER_USE.DISCOUNT_PERCENT}% OFF</span>
                    </div>
                  </div>
                  <ul className={`space-y-4 ${isDark ? 'text-gray-300' : 'text-gray-600'} text-base sm:text-lg mb-6`}>
                    <li className="flex items-center">
                      <span className="text-purple-400 mr-2">✓</span>
                      {t('membership.cards.pay_per_use.features.high_insight')}
                    </li>
                    <li className="flex items-center">
                      <span className="text-purple-400 mr-2">✓</span>
                      {t('membership.cards.pay_per_use.features.save_forever')}
                    </li>
                  </ul>
                  <div className="mb-6">
                    <label className="text-gray-300 mb-2 block">{t('membership.cards.pay_per_use.count_selector.label')}</label>
                    <div className="relative flex items-center">
                      <input 
                        type="number"
                        min="1"
                        max="100"
                        placeholder={t('membership.cards.pay_per_use.count_selector.placeholder')}
                        className="w-full bg-black/40 border border-purple-500/30 rounded-lg px-4 py-2 text-white focus:outline-none focus:border-purple-500 [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                        onChange={(e) => {
                          const value = e.target.value;
                          if (value === '') {
                            setUseCount(0);
                            setSelectedPlan({ price: 0 });
                            return;
                          }
                          const count = Math.min(Math.max(parseInt(value), 1), 100);
                          setUseCount(count);
                          // 根据当前货币选择正确的价格
                          const pricePerUse = currency === 'CNY' 
                            ? INTERNAL_PRICING_CNY.PAY_PER_USE.PRICE_PER_USE 
                            : INTERNAL_PRICING_USD.PAY_PER_USE.PRICE_PER_USE;
                          setSelectedPlan({ price: count * pricePerUse });
                          if (count.toString() !== value) {
                            e.target.value = count.toString();
                          }
                        }}
                        value={useCount}
                      />
                      <div className="absolute right-2 top-1/2 -translate-y-1/2 flex items-center space-x-1">
                        <div className="flex flex-col space-y-0.5">
                          <button
                            onClick={() => {
                              const newCount = Math.min(useCount + 1, 100);
                              setUseCount(newCount);
                              // 根据当前货币选择正确的价格
                              const pricePerUse = currency === 'CNY' 
                                ? INTERNAL_PRICING_CNY.PAY_PER_USE.PRICE_PER_USE 
                                : INTERNAL_PRICING_USD.PAY_PER_USE.PRICE_PER_USE;
                              setSelectedPlan({ price: newCount * pricePerUse });
                            }}
                            className="group w-6 h-4 flex items-center justify-center transition-colors"
                            type="button"
                          >
                            <svg className="w-3.5 h-3.5 text-purple-400 group-hover:text-purple-300 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2.5" d="M5 15l7-7 7 7" />
                            </svg>
                          </button>
                          <button
                            onClick={() => {
                              const newCount = Math.max(useCount - 1, 1);
                              setUseCount(newCount);
                              // 根据当前货币选择正确的价格
                              const pricePerUse = currency === 'CNY' 
                                ? INTERNAL_PRICING_CNY.PAY_PER_USE.PRICE_PER_USE 
                                : INTERNAL_PRICING_USD.PAY_PER_USE.PRICE_PER_USE;
                              setSelectedPlan({ price: newCount * pricePerUse });
                            }}
                            className="group w-6 h-4 flex items-center justify-center transition-colors"
                            type="button"
                          >
                            <svg className="w-3.5 h-3.5 text-purple-400 group-hover:text-purple-300 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2.5" d="M19 9l-7 7-7-7" />
                            </svg>
                          </button>
                        </div>
                        <span className="text-gray-400 text-sm">{t('membership.cards.pay_per_use.count_selector.unit')}</span>
                      </div>
                    </div>
                  </div>
                </div>
                <button
                  onClick={() => handleSubscribe(useCount * INTERNAL_PRICING_USD.PAY_PER_USE.PRICE_PER_USE, 'pay_per_use')}
                  disabled={loading || currentPlan === 'monthly' || currentPlan === 'yearly' || useCount === 0}
                  className={`w-full mt-8 px-6 py-3 bg-purple-600 hover:bg-purple-500 active:bg-purple-700 text-white rounded-xl transition-colors ${(loading || currentPlan === 'monthly' || currentPlan === 'yearly' || useCount === 0) ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  <span className="text-white font-medium" style={{ color: '#ffffff' }}>
                    {loading ? t('membership.cards.pay_per_use.button.processing') : t('membership.cards.pay_per_use.button.subscribe')}
                  </span>
                </button>
              </div>
            </div>
          </div>

          <BlackTextModal
            open={showPaymentModal}
            onClose={handleClosePayment}
            className="w-[360px]"
          >
            <div className="bg-white rounded-xl w-full shadow-2xl">
              <div className="relative px-6 py-4">
                <button
                  onClick={handleClosePayment}
                  className="absolute right-4 top-4 text-gray-400 hover:text-gray-500 transition-colors"
                >
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fillRule="evenodd"
                      d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>
              </div>

              {!orderId && (
                <div className="px-6">
                  <div className="text-center mb-4">
                    <div className="text-base text-gray-500 font-sans" style={{ color: '#000000 !important' }}>{t('membership.payment_amount')}</div>
                    <div className="text-4xl font-bold mt-2 text-gray-800 font-din" style={{ color: '#000000 !important' }}>
                      {currency === 'CNY' ? '￥' : '$'}
                      <span className="font-sans" style={{ color: '#000000 !important' }}>
                        {currency === 'CNY' 
                          ? pendingOrder?.planName === 'monthly'
                            ? INTERNAL_PRICING_CNY.MONTHLY.DISPLAY_PRICE
                            : pendingOrder?.planName === 'yearly'
                              ? INTERNAL_PRICING_CNY.YEARLY.DISPLAY_PRICE
                              : (selectedPlan?.price || 0).toFixed(2)
                          : pendingOrder?.planName === 'monthly'
                            ? INTERNAL_PRICING_USD.MONTHLY.DISPLAY_PRICE
                            : pendingOrder?.planName === 'yearly'
                              ? INTERNAL_PRICING_USD.YEARLY.DISPLAY_PRICE
                              : (selectedPlan?.price || 0).toFixed(2)}
                      </span>
                    </div>
                  </div>

                  <div className="mb-4">
                    <div className="inline-flex p-1 bg-gray-100 rounded-full">
                      <button
                        onClick={() => {
                          setCurrency('USD');
                          setPaymentMethod('paypal');
                          setOrderId('');
                          setQrCodeUrl('');
                          
                          // 如果是按次付费，根据新货币更新价格
                          if (pendingOrder?.planName === 'pay_per_use') {
                            setSelectedPlan({ 
                              price: useCount * INTERNAL_PRICING_USD.PAY_PER_USE.PRICE_PER_USE
                            });
                          }
                        }}
                        className={`px-4 py-2 rounded-full text-sm font-medium transition-colors font-sans ${
                          currency === 'USD'
                            ? 'bg-white text-gray-900 shadow'
                            : 'text-gray-500 hover:text-gray-700'
                        }`}
                        style={{ color: currency === 'USD' ? '#111827 !important' : '#6B7280 !important' }}
                      >
                        USD
                      </button>
                      <button
                        onClick={() => {
                          setCurrency('CNY');
                          if (paymentMethod === 'paypal') {
                            // 检查是否为移动设备
                            const isMobile = isMobileDevice();
                            // 如果是移动设备，默认使用支付宝
                            setPaymentMethod(isMobile ? 'alipay' : 'wechat');
                          }
                          setOrderId('');
                          setQrCodeUrl('');
                          
                          // 如果是按次付费，根据新货币更新价格
                          if (pendingOrder?.planName === 'pay_per_use') {
                            setSelectedPlan({ 
                              price: useCount * INTERNAL_PRICING_CNY.PAY_PER_USE.PRICE_PER_USE
                            });
                          }
                        }}
                        className={`px-4 py-2 rounded-full text-sm font-medium transition-colors font-sans ${
                          currency === 'CNY'
                            ? 'bg-white text-gray-900 shadow'
                            : 'text-gray-500 hover:text-gray-700'
                        }`}
                        style={{ color: currency === 'CNY' ? '#111827 !important' : '#6B7280 !important' }}
                      >
                        CNY
                      </button>
                    </div>
                  </div>

                  <div className="space-y-3">
                    {currency === 'CNY' ? (
                      <>
                        {(() => {
                          // 使用新的设备检测函数
                          const isMobile = isMobileDevice();
                          
                          // 移动端：支付宝在上，微信在下
                          if (isMobile) {
                            return (
                              <>
                                <button
                                  onClick={() => handlePaymentMethodChange('alipay')}
                                  className={`w-full py-3 px-4 rounded-lg flex items-center justify-between border ${
                                    paymentMethod === 'alipay'
                                      ? 'border-blue-500 bg-blue-50'
                                      : 'border-gray-200'
                                  } transition-colors`}
                                >
                                  <div className="flex items-center space-x-3">
                                    <img src="/alipay-logo.png" alt="支付宝" className="h-8" />
                                  </div>
                                  <div className="w-5 h-5 rounded-full border border-gray-300 flex items-center justify-center">
                                    {paymentMethod === 'alipay' && (
                                      <div className="w-3 h-3 rounded-full bg-blue-500" />
                                    )}
                                  </div>
                                </button>
                                <button
                                  onClick={() => handlePaymentMethodChange('wechat')}
                                  className={`w-full py-3 px-4 rounded-lg flex items-center justify-between border ${
                                    paymentMethod === 'wechat'
                                      ? 'border-blue-500 bg-blue-50'
                                      : 'border-gray-200'
                                  } transition-colors`}
                                >
                                  <div className="flex items-center space-x-3">
                                    <img src="/wechat-logo.png" alt="微信" className="h-8" />
                                  </div>
                                  <div className="w-5 h-5 rounded-full border border-gray-300 flex items-center justify-center">
                                    {paymentMethod === 'wechat' && (
                                      <div className="w-3 h-3 rounded-full bg-blue-500" />
                                    )}
                                  </div>
                                </button>
                              </>
                            );
                          } else {
                            // PC端：保持原来的顺序，微信在上，支付宝在下
                            return (
                              <>
                                <button
                                  onClick={() => handlePaymentMethodChange('wechat')}
                                  className={`w-full py-3 px-4 rounded-lg flex items-center justify-between border ${
                                    paymentMethod === 'wechat'
                                      ? 'border-blue-500 bg-blue-50'
                                      : 'border-gray-200'
                                  } transition-colors`}
                                >
                                  <div className="flex items-center space-x-3">
                                    <img src="/wechat-logo.png" alt="微信" className="h-8" />
                                  </div>
                                  <div className="w-5 h-5 rounded-full border border-gray-300 flex items-center justify-center">
                                    {paymentMethod === 'wechat' && (
                                      <div className="w-3 h-3 rounded-full bg-blue-500" />
                                    )}
                                  </div>
                                </button>
                                <button
                                  onClick={() => handlePaymentMethodChange('alipay')}
                                  className={`w-full py-3 px-4 rounded-lg flex items-center justify-between border ${
                                    paymentMethod === 'alipay'
                                      ? 'border-blue-500 bg-blue-50'
                                      : 'border-gray-200'
                                  } transition-colors`}
                                >
                                  <div className="flex items-center space-x-3">
                                    <img src="/alipay-logo.png" alt="支付宝" className="h-8" />
                                  </div>
                                  <div className="w-5 h-5 rounded-full border border-gray-300 flex items-center justify-center">
                                    {paymentMethod === 'alipay' && (
                                      <div className="w-3 h-3 rounded-full bg-blue-500" />
                                    )}
                                  </div>
                                </button>
                              </>
                            );
                          }
                        })()}
                      </>
                    ) : null}
                  </div>

                  {currency === 'USD' && (
                    <div className="h-[200px] relative">
                      {!showPayPalButton && (
                        <div className="absolute inset-0 flex items-center justify-center">
                          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
                        </div>
                      )}
                      <div className={`absolute inset-0 transition-opacity duration-500 ${showPayPalButton ? 'opacity-100 z-10' : 'opacity-0'}`}>
                        {renderPaymentMethod()}
                      </div>
                    </div>
                  )}

                  {!orderId && paymentMethod !== 'paypal' && (
                    <button
                      onClick={handleCreateOrder}
                      disabled={loading}
                      className="w-full mt-6 mb-4 py-3 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:bg-gray-300"
                    >
                      <span style={{ color: '#FFFFFF !important' }}>
                        {loading ? '正在创建订单...' : '确认支付'}
                      </span>
                    </button>
                  )}
                </div>
              )}

              {orderId && (
                <div className="px-6 pb-6">
                  <div className="text-center">
                    {showSuccess ? (
                      <div className="py-12 space-y-8">
                        <div className="flex justify-center">
                          <CheckmarkAnimation size={120} color="#4CAF50" strokeWidth={6} />
                        </div>
                        <div className="text-center space-y-2">
                          <p className="text-2xl font-semibold text-gray-800 font-sans" style={{ color: '#1F2937 !important' }}>{t('membership.payment_success')}</p>
                        </div>
                      </div>
                    ) : paymentMethod === 'wechat' ? (
                      <div className="text-center">
                        {qrCodeUrl && (
                          <>
                            <div className="flex justify-center mb-6">
                              <img src="/wechat-logo.png" alt="微信支付" className="h-8" />
                            </div>
                            <div className="text-center mb-4">
                              <div className="text-base text-gray-500 font-sans" style={{ color: '#000000 !important' }}>{t('membership.wechat_scan_prompt')}</div>
                              <div className="text-4xl font-bold mt-2 text-gray-800 font-din" style={{ color: '#000000 !important' }}>
                                <span style={{ color: '#000000 !important' }}>￥</span>
                                <span className="font-sans" style={{ color: '#000000 !important' }}>
                                  {pendingOrder?.planName === 'monthly'
                                    ? INTERNAL_PRICING_CNY.MONTHLY.DISPLAY_PRICE
                                    : pendingOrder?.planName === 'yearly'
                                      ? INTERNAL_PRICING_CNY.YEARLY.DISPLAY_PRICE
                                      : (selectedPlan?.price || 0).toFixed(2)}
                                </span>
                              </div>
                            </div>
                            <div className="mb-4 flex justify-center">
                              <div className="border p-3 rounded-lg bg-white">
                                <QRCodeWithAnimation url={qrCodeUrl} showSuccess={showSuccess} />
                              </div>
                            </div>
                            <div className="text-sm text-gray-500 font-sans" style={{ color: '#6B7280 !important' }}>
                              {showSuccess ? '支付成功！' : t('membership.wechat_scan_prompt')}
                            </div>
                          </>
                        )}
                      </div>
                    ) : paymentMethod === 'paypal' ? (
                      <div className="py-12 space-y-6">
                        <div className="flex flex-col items-center">
                          <img src="/paypal-logo.png" alt="PayPal" className="h-10 mb-8" />
                          <LoadingSpinner />
                          <p className="text-lg text-gray-700 mt-6 font-sans" style={{ color: '#374151 !important' }}>{t('membership.paypal_verifying')}</p>
                        </div>
                      </div>
                    ) : (
                      <div className="py-8 space-y-6">
                        <div className="flex justify-center">
                          <img src="/alipay-logo.png" alt="支付宝" className="h-12" />
                        </div>
                        <div className="space-y-2">
                          <p className="text-lg font-medium text-gray-800 font-sans" style={{ color: '#000000 !important' }}>{t('membership.alipay_opened')}</p>
                          <p className="text-base text-gray-600 font-sans" style={{ color: '#000000 !important' }}>
                            {t('membership.alipay_amount', { 
                              amount: pendingOrder?.planName === 'monthly' 
                                ? INTERNAL_PRICING_CNY.MONTHLY.DISPLAY_PRICE
                                : pendingOrder?.planName === 'yearly' 
                                  ? INTERNAL_PRICING_CNY.YEARLY.DISPLAY_PRICE
                                  : (useCount * INTERNAL_PRICING_CNY.PAY_PER_USE.PRICE_PER_USE).toFixed(2) 
                            })}
                          </p>
                          <p className="text-sm text-gray-500 font-sans" style={{ color: '#000000 !important' }}>{t('membership.alipay_complete_prompt')}</p>
                        </div>
                        <button
                          onClick={handlePaymentConfirmation}
                          disabled={loading}
                          className="w-full py-3 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed font-sans"
                        >
                          {loading ? (
                            <div className="flex items-center justify-center space-x-2">
                              <div className="w-5 h-5 border-t-2 border-b-2 border-white rounded-full animate-spin"></div>
                              <span style={{ color: '#FFFFFF !important' }}>{t('membership.confirming')}</span>
                            </div>
                          ) : (
                            <span style={{ color: '#FFFFFF !important' }}>{t('membership.alipay_complete')}</span>
                          )}
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </BlackTextModal>
        </div>
      </div>
      <div className="relative z-10">
        <div className="mt-24 sm:mt-48 max-w-5xl mx-auto px-4 sm:px-6">
          <h2 className={`text-center text-2xl sm:text-4xl font-bold ${isDark ? 'text-purple-400' : 'text-purple-600'} mb-6 sm:mb-12 font-sans`}>{t('membership.faqs.title')}</h2>
          
          <div className="space-y-3 sm:space-y-4 font-sans">
            <div className={`border-b ${isDark ? 'border-gray-700/30' : 'border-gray-300/50'} pb-3 sm:pb-4`}>
              <button
                onClick={() => toggleFaq('payment')}
                className="w-full flex items-center justify-between text-left"
              >
                <div className="flex items-center">
                  <span className="text-purple-400 text-lg sm:text-xl font-light mr-2 sm:mr-3">
                    {expandedFaqs.payment ? '−' : '+'}
                  </span>
                  <span className={`${isDark ? 'text-white' : 'text-gray-800'} text-base sm:text-lg`}>{t('membership.faqs.payment')}</span>
                </div>
              </button>
              {expandedFaqs.payment && (
                <EnhancedFaqContent htmlContent={t('membership.faqs.payment_prompt')} isDark={isDark} />
              )}
            </div>

            <div className={`border-b ${isDark ? 'border-gray-700/30' : 'border-gray-300/50'} pb-3 sm:pb-4`}>
              <button
                onClick={() => toggleFaq('password')}
                className="w-full flex items-center justify-between text-left"
              >
                <div className="flex items-center">
                  <span className="text-purple-400 text-lg sm:text-xl font-light mr-2 sm:mr-3">
                    {expandedFaqs.password ? '−' : '+'}
                  </span>
                  <span className={`${isDark ? 'text-white' : 'text-gray-800'} text-base sm:text-lg`}>{t('membership.faqs.subscription')}</span>
                </div>
              </button>
              {expandedFaqs.password && (
                <EnhancedFaqContent htmlContent={t('membership.faqs.subscription_prompt')} isDark={isDark} />
              )}
            </div>

            <div className={`border-b ${isDark ? 'border-gray-700/30' : 'border-gray-300/50'} pb-3 sm:pb-4`}>
              <button
                onClick={() => toggleFaq('cancel')}
                className="w-full flex items-center justify-between text-left"
              >
                <div className="flex items-center">
                  <span className="text-purple-400 text-lg sm:text-xl font-light mr-2 sm:mr-3">
                    {expandedFaqs.cancel ? '−' : '+'}
                  </span>
                  <span className={`${isDark ? 'text-white' : 'text-gray-800'} text-base sm:text-lg`}>{t('membership.faqs.cancel')}</span>
                </div>
              </button>
              {expandedFaqs.cancel && (
                <EnhancedFaqContent htmlContent={t('membership.faqs.cancel_prompt')} isDark={isDark} />
              )}
            </div>

            <div className={`border-b ${isDark ? 'border-gray-700/30' : 'border-gray-300/50'} pb-3 sm:pb-4`}>
              <button
                onClick={() => toggleFaq('howToCancel')}
                className="w-full flex items-center justify-between text-left"
              >
                <div className="flex items-center">
                  <span className="text-purple-400 text-lg sm:text-xl font-light mr-2 sm:mr-3">
                    {expandedFaqs.howToCancel ? '−' : '+'}
                  </span>
                  <span className={`${isDark ? 'text-white' : 'text-gray-800'} text-base sm:text-lg`}>{t('membership.faqs.how_to_cancel')}</span>
                </div>
              </button>
              {expandedFaqs.howToCancel && (
                <EnhancedFaqContent htmlContent={t('membership.faqs.how_to_cancel_prompt')} isDark={isDark} />
              )}
            </div>

            <div className={`border-b ${isDark ? 'border-gray-700/30' : 'border-gray-300/50'} pb-3 sm:pb-4`}>
              <button
                onClick={() => toggleFaq('priceChange')}
                className="w-full flex items-center justify-between text-left"
              >
                <div className="flex items-center">
                  <span className="text-purple-400 text-lg sm:text-xl font-light mr-2 sm:mr-3">
                    {expandedFaqs.priceChange ? '−' : '+'}
                  </span>
                  <span className={`${isDark ? 'text-white' : 'text-gray-800'} text-base sm:text-lg`}>{t('membership.faqs.price_change')}</span>
                </div>
              </button>
              {expandedFaqs.priceChange && (
                <EnhancedFaqContent htmlContent={t('membership.faqs.price_change_prompt')} isDark={isDark} />
              )}
            </div>

            <div className={`border-b ${isDark ? 'border-gray-700/30' : 'border-gray-300/50'} pb-3 sm:pb-4`}>
              <button
                onClick={() => toggleFaq('changePlan')}
                className="w-full flex items-center justify-between text-left"
              >
                <div className="flex items-center">
                  <span className="text-purple-400 text-lg sm:text-xl font-light mr-2 sm:mr-3">
                    {expandedFaqs.changePlan ? '−' : '+'}
                  </span>
                  <span className={`${isDark ? 'text-white' : 'text-gray-800'} text-base sm:text-lg`}>{t('membership.faqs.change_plan')}</span>
                </div>
              </button>
              {expandedFaqs.changePlan && (
                <EnhancedFaqContent htmlContent={t('membership.faqs.change_plan_prompt')} isDark={isDark} />
              )}
            </div>
          </div>
        </div>
        <div className="mt-48">
          <Footer />
        </div>
      </div>
      
      <LoginPrompt
        isOpen={showLoginPrompt}
        onClose={() => setShowLoginPrompt(false)}
      />

      <BlackTextModal
        open={isVerifying}
        onClose={() => {}}
        className="w-[360px]"
      >
        <div className="bg-white rounded-xl w-full shadow-2xl">
          <div className="py-8 space-y-6">
            {showSuccess ? (
              <div className="py-12 space-y-8">
                <div className="flex justify-center">
                  <CheckmarkAnimation size={120} color="#4CAF50" strokeWidth={6} />
                </div>
                <div className="text-center space-y-2">
                  <p className="text-2xl font-semibold text-gray-800 font-sans" style={{ color: '#1F2937 !important' }}>
                    {pendingOrder?.planName === 'pay_per_use' 
                      ? t('membership.success.pay_per_use_activated')
                      : t('membership.paypal_success')}
                  </p>
                </div>
              </div>
            ) : (
              <div className="py-12 space-y-6">
                <div className="flex flex-col items-center">
                  <img src="/paypal-logo.png" alt="PayPal" className="h-10 mb-8" />
                  <LoadingSpinner />
                  <p className="text-lg text-gray-700 mt-6 font-sans" style={{ color: '#374151 !important' }}>{t('membership.paypal_verifying')}</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </BlackTextModal>

    </div>
  );
}

export { Membership as default };