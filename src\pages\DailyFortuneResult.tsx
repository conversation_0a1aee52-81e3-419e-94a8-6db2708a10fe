import React, { useEffect, useState, Suspense } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
// import { toast } from 'react-hot-toast';
import Footer from '../components/Footer';
import LandingBackground from '../components/LandingBackground';
import { AnalysisSection, DailyFortuneCard } from '../components/daily-fortune';
import SEO from '../components/SEO';
import { useTheme } from '../contexts/ThemeContext';
import { TAROT_CARDS } from '../data/tarot-cards';
import SpotlightCard from '../blocks/Components/SpotlightCard/SpotlightCard';
import { getFortuneBySessionId } from '../services/fortuneService';

/**
 * 每日运势结果页面
 * 该页面展示每日运势的详细解读结果
 */
const DailyFortuneResult: React.FC = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const { theme } = useTheme();
  const [fortuneResults, setFortuneResults] = useState({});
  const [fortuneRatings, setFortuneRatings] = useState({});
  const [isLoading, setIsLoading] = useState(true);
  
  // 从URL中获取sessionId
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const sessionIdFromUrl = queryParams.get('sessionId');
  
  // 卡牌相关状态
  const [selectedCard, setSelectedCard] = useState<number | null>(null);
  const [cardImage, setCardImage] = useState<string | null>(null);
  const [cardOrientation, setCardOrientation] = useState(false); // false为正位，true为逆位



  // 获取字体样式的函数
  const getFontClass = () => {
    switch (i18n.language) {
      case 'ja':
        return 'font-sans japanese';
      case 'zh-CN':
      case 'zh-TW':
        return 'font-sans chinese';
      default:
        return 'font-sans';
    }
  };

  // 获取今天的日期字符串（YYYY-MM-DD格式）
  const getTodayDateString = () => {
    const today = new Date();
    return `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
  };
  
  // 获取格式化的今日日期显示
  const getFormattedTodayDate = () => {
    const today = new Date();
    
    // 根据当前语言返回不同格式的日期
    switch (i18n.language) {
      case 'ja':
        return `${today.getFullYear()}年${today.getMonth() + 1}月${today.getDate()}日`;
      case 'en':
        // 英文月份缩写
        const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        return `${monthNames[today.getMonth()]} ${today.getDate()}, ${today.getFullYear()}`;
      case 'zh-CN':
      case 'zh-TW':
      default:
        return `${today.getFullYear()}年${today.getMonth() + 1}月${today.getDate()}日`;
    }
  };

  // 从本地存储或通过sessionId从服务器加载运势结果和卡牌数据
  useEffect(() => {
    // 优先检查本地缓存
    const cachedFortuneStr = localStorage.getItem('dailyFortuneCache');
    
    // 如果URL中有sessionId，使用它从服务器获取数据
    if (sessionIdFromUrl) {
      // 保存到localStorage以便后续使用
      localStorage.setItem('sessionId', sessionIdFromUrl);
      
      // 尝试从服务器获取运势数据
      const fetchFortuneData = async () => {
        try {
          const response = await getFortuneBySessionId(sessionIdFromUrl);
          
          if (response.success && response.data) {
            const { fortuneResult, cardData } = response.data;
            
            // 处理运势结果
            // 如果fortuneResult是字符串，尝试解析为JSON对象
            let parsedFortuneResult = fortuneResult;
            if (typeof fortuneResult === 'string') {
              try {
                parsedFortuneResult = JSON.parse(fortuneResult);
              } catch (error) {
                // console.error('解析fortuneResult字符串失败:', error);
                // 解析失败时继续使用原始字符串
              }
            }
            
            // 处理解析后的fortuneResult
            if (parsedFortuneResult && typeof parsedFortuneResult === 'object') {
              // 检查是否有sections字段
              if (parsedFortuneResult.sections && typeof parsedFortuneResult.sections === 'object') {
                setFortuneResults({
                  intro: parsedFortuneResult.sections.intro || '',
                  overall: parsedFortuneResult.sections.overall || '',
                  love: parsedFortuneResult.sections.love || '',
                  career: parsedFortuneResult.sections.career || '',
                  wealth: parsedFortuneResult.sections.wealth || '',
                  health: parsedFortuneResult.sections.health || '',
                  mystery: parsedFortuneResult.sections.mystery || ''
                });
                
                // 处理评分数据
                if (parsedFortuneResult.ratings && typeof parsedFortuneResult.ratings === 'object') {
                  setFortuneRatings(parsedFortuneResult.ratings);
                }
              } else if (parsedFortuneResult.reading) {
                // 如果有reading字段，尝试解析
                try {
                  const readingObj = typeof parsedFortuneResult.reading === 'string' 
                    ? JSON.parse(parsedFortuneResult.reading) 
                    : parsedFortuneResult.reading;
                  
                  if (readingObj.sections) {
                    setFortuneResults({
                      intro: readingObj.sections.intro || '',
                      overall: readingObj.sections.overall || '',
                      love: readingObj.sections.love || '',
                      career: readingObj.sections.career || '',
                      wealth: readingObj.sections.wealth || '',
                      health: readingObj.sections.health || '',
                      mystery: readingObj.sections.mystery || ''
                    });
                    
                    if (readingObj.ratings) {
                      setFortuneRatings(readingObj.ratings);
                    }
                  }
                } catch (parseError) {
                  // console.error('解析reading字段失败:', parseError);
                }
              }
              
              // 处理卡牌数据
              if (cardData && cardData.length > 0) {
                const card = cardData[0];
                
                // 查找卡牌索引
                let cardIndex = card.id;
                if (cardIndex === undefined || cardIndex === null) {
                  // 如果没有id，尝试通过名称查找
                  cardIndex = TAROT_CARDS.findIndex(c => c.name === card.name || c.nameEn === card.nameEn);
                  if (cardIndex === -1) cardIndex = 0; // 默认使用第一张卡牌
                }
                
                setSelectedCard(cardIndex);
                setCardOrientation(card.isReversed || false);
                setCardImage(`/images-optimized/tarot/${TAROT_CARDS[cardIndex].nameEn}.webp`);
              }
              
              // 缓存到本地
              const fortuneCache = {
                date: getTodayDateString(),
                content: JSON.stringify(parsedFortuneResult),
                cardData: cardData || [],
                spread: response.data.spreadData || { id: "daily-fortune", name: "每日运势", description: "抽取一张塔罗牌，了解今日运势", cardCount: 1 }
              };
              localStorage.setItem('dailyFortuneCache', JSON.stringify(fortuneCache));
              
              setIsLoading(false);
              return;
            } else {
              // console.error('服务器返回的数据格式不正确');
              navigate('/daily-fortune');
              return;
            }
          } else {
            // console.error('服务器返回错误或无数据');
            navigate('/daily-fortune');
            return;
          }
        } catch (error) {
          // console.error('从服务器获取运势数据失败:', error);
          // 如果从服务器获取失败，尝试从本地缓存加载
          loadFromLocalCache();
        }
      };
      
      fetchFortuneData();
    } 
    // 如果有本地缓存且没有sessionId，直接使用缓存
    else if (cachedFortuneStr) {
      loadFromLocalCache();
    } 
    // 如果既没有sessionId也没有本地缓存，返回到每日运势页面
    else {
      navigate('/daily-fortune');
    }
    
    // 从本地缓存加载运势解读结果的函数
    function loadFromLocalCache() {
      const cachedFortuneStr = localStorage.getItem('dailyFortuneCache');
      
      if (cachedFortuneStr) {
        try {
          const cachedFortune = JSON.parse(cachedFortuneStr);
          
          // 检查缓存是否是今天的
          if (cachedFortune.date === getTodayDateString()) {
            // 如果有内容，解析运势结果
            if (cachedFortune.content && cachedFortune.content.trim()) {
              try {
                // 尝试解析内容为JSON（服务器已经解析好的结果）
                const contentObj = JSON.parse(cachedFortune.content);
                
                // 检查是否有sections字段（服务器预解析的部分）
                if (contentObj.sections && typeof contentObj.sections === 'object') {
                  // 直接使用服务器解析的部分
                  setFortuneResults({
                    intro: contentObj.sections.intro || '',
                    overall: contentObj.sections.overall || '',
                    love: contentObj.sections.love || '',
                    career: contentObj.sections.career || '',
                    wealth: contentObj.sections.wealth || '',
                    health: contentObj.sections.health || '',
                    mystery: contentObj.sections.mystery || ''
                  });
                  
                  // 处理评分数据
                  if (contentObj.ratings && typeof contentObj.ratings === 'object') {
                    setFortuneRatings(contentObj.ratings);
                  }
                } else {
                  // 如果没有sections字段，显示错误信息
                  // console.error('服务器返回的数据格式不正确，缺少sections字段');
                  navigate('/daily-fortune');
                  return;
                }
              } catch (parseError) {
                // 如果解析失败，显示错误信息
                // console.error('解析运势结果失败:', parseError);
                navigate('/daily-fortune');
                return;
              }
              
              setIsLoading(false);
              
              // 设置卡牌数据
              if (cachedFortune.cardData && cachedFortune.cardData.length > 0) {
                const cardData = cachedFortune.cardData[0];
                
                // 查找卡牌索引
                let cardIndex = cardData.id;
                if (cardIndex === undefined || cardIndex === null) {
                  // 如果没有id，尝试通过名称查找
                  cardIndex = TAROT_CARDS.findIndex(card => card.name === cardData.name || card.nameEn === cardData.nameEn);
                  if (cardIndex === -1) cardIndex = 0; // 默认使用第一张卡牌
                }
                
                setSelectedCard(cardIndex);
                setCardOrientation(cardData.isReversed || false);
                setCardImage(`/images-optimized/tarot/${TAROT_CARDS[cardIndex].nameEn}.webp`);
              }
            } else {
              // 如果没有内容，返回到每日运势页面
              navigate('/daily-fortune');
            }
          } else {
            // 不是今天的预测，返回到每日运势页面
            navigate('/daily-fortune');
          }
        } catch (error) {
          // console.error('解析缓存的运势数据失败:', error);
          navigate('/daily-fortune');
        }
      } else {
        // 没有缓存数据，返回到每日运势页面
        navigate('/daily-fortune');
      }
    }
  }, [navigate, sessionIdFromUrl]);

  return (
    <div className="min-h-screen flex flex-col relative text-white antialiased">
      <SEO 
        title={t('daily.result.title', '每日运势解读结果')}
        description={t('daily.result.description', '查看你的每日塔罗运势解读结果，包含整体、爱情、事业、财富和健康等多方面的个性化指引。')}
      />
      <LandingBackground />
      
      <div className="flex-grow relative z-10 pt-8">
        <div className="container mx-auto px-2 sm:px-4 md:px-6 lg:px-8">
          {isLoading ? (
            <div className="text-center py-20">
              <div className="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
              <p className="mt-4 text-lg">{t('daily.result.loading', '正在加载运势解读结果...')}</p>
            </div>
          ) : (
            <>
              {/* 主标题和副标题 */}
              <div className="text-center mb-10">
                <h1 className={`main-title mb-2 sm:mb-3 ${getFontClass()} dark:text-white text-gray-900`}>
                  {t('daily.analysis.title', '今日運勢深度解析')}
                </h1>
                <p className={`text-base sm:text-lg dark:text-purple-300 text-purple-600 italic ${getFontClass()}`}>
                  {getFormattedTodayDate()}
                </p>
              </div>
              
              {/* 添加卡牌展示组件 */}
              <div className="mb-10">
                <DailyFortuneCard
                  cardBackImage="" // 结果页面不需要卡背图案
                  flipped={true}
                  canPredictToday={false} // 在结果页面不允许重新抽取
                  cardImage={cardImage}
                  selectedCard={selectedCard}
                  cardOrientation={cardOrientation}
                  tarotCards={TAROT_CARDS}
                  handleCardFlip={() => {}} // 空函数，在结果页面不需要翻牌功能
                  getFontClass={getFontClass}
                />
              </div>
              

              {/* 运势解析部分 */}
              <AnalysisSection 
                getFontClass={getFontClass} 
                fortuneResults={fortuneResults} 
                fortuneRatings={fortuneRatings}
                isMainTitle={false}
                hideTitle={true}
              />
              
              {/* 底部SpotlightCard组件 */}
              <Suspense
                fallback={
                  <div className="w-full h-[200px] bg-gray-900 rounded-lg animate-pulse" />
                }
              >
                <div className="spotlight-section py-24 md:py-32 mt-16">
                  <div className="max-w-3xl mx-auto px-2 sm:px-4">
                    <SpotlightCard
                      className="custom-spotlight-card"
                      spotlightColor="rgba(168, 85, 247, 0.2)"
                    >
                      <div className="p-4 sm:p-8 text-center">
                        <h3
                          className="text-2xl md:text-3xl font-semibold mb-4"
                          style={{
                            background: theme === 'light' 
                              ? "none" 
                              : "linear-gradient(135deg, #E2E8FF, #FFF1F2, #FAF5FF)",
                            WebkitBackgroundClip: theme === 'light' ? "inherit" : "text",
                            WebkitTextFillColor: theme === 'light' ? "#000" : "transparent",
                            color: theme === 'light' ? "#000" : "inherit"
                          }}
                        >
                          {t("home.explore_section.title", "探索更多塔罗解读")}
                        </h3>
                        <p className={`${
                          theme === 'light' ? 'text-gray-700' : 'text-gray-300'
                        } text-lg md:text-xl mb-6 px-1`}>
                          {t("home.explore_section.description", "想要获得更深入的塔罗指引？尝试我们的专业塔罗牌阵解读")}
                        </p>
                        <div className="flex justify-center">
                          <motion.button
                            onClick={() => navigate('/tarot')}
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            className="px-6 py-3 rounded-full"
                            style={{
                              background:
                                theme === 'light'
                                  ? "linear-gradient(135deg, rgba(124, 58, 237, 0.7), rgba(168, 85, 247, 0.7))"
                                  : "linear-gradient(135deg, rgba(124, 58, 237, 0.8), rgba(168, 85, 247, 0.8))",
                              color: '#FFFFFF',
                            }}
                          >
                            {t("home.explore_section.button", "开始塔罗之旅")}
                          </motion.button>
                        </div>
                      </div>
                    </SpotlightCard>
                  </div>
                </div>
              </Suspense>
            </>
          )}
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default DailyFortuneResult; 