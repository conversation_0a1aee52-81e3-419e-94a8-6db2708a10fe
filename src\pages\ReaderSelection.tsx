import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
// import { motion } from 'framer-motion';
import { updateSession } from '../services/sessionService';
import Footer from '../components/Footer';
import LandingBackground from '../components/LandingBackground';
import '../styles/GradientStyles.css';
import '../styles/fonts.css';
import '../styles/ReaderSelection.css';
import VipPromptDialog from '../components/VipPromptDialog';
import LoginPrompt from '../components/LoginPrompt';

import { useUser } from '../contexts/UserContext';
import { useTheme } from '../contexts/ThemeContext';
import SEO from '../components/SEO';
import { checkRavenUsed } from '../services/userService';
import { fetchParagraphAudio } from '../components/speech/audioUtils';
import { 
  getReaderVotes, 
  getUserVotedReaders,
  toggleReaderVoteLocal,
  submitPendingVotes,
  getPendingVotesCount
} from '../services/readerService';
import { toast } from 'react-hot-toast';
import { formatNumber } from '../utils/numberUtils';
import { useLanguageNavigate } from '../hooks/useLanguageNavigate';
import { CdnLazyImage } from '../components/CdnImageExport';

interface Reader {
  id: string;
  nameEn: string;
  price: 'free' | 'paid';
  isFreeFirstTime?: boolean; // 添加标识是否为首次免费使用
  voteCount?: number; // 添加投票数字段
  hasVoted?: boolean; // 添加用户是否已投票字段
}

// 定义价格标签的接口
interface PriceLabel {
  text: string;
  className: string;
  style?: React.CSSProperties;
}

const defaultReaders: Reader[] = [
  {
    id: 'basic',
    nameEn: 'Molly',
    price: 'free',
    voteCount: 1500  // 茉伊排第一，点赞数最高
  },
  {
    id: 'elias',
    nameEn: 'Elias',
    price: 'paid',
    voteCount: 132  // 林曜排第五
  },
  {
    id: 'claire',
    nameEn: 'Claire',
    price: 'paid',
    voteCount: 251  // 苏谨排第四
  },
  {
    id: 'raven',
    nameEn: 'Raven',
    price: 'paid',
    voteCount: 743  // 渡鸦排第二
  },
  {
    id: 'aurora',
    nameEn: 'Aurora',
    price: 'paid',
    voteCount: 86   // 月熙排第六，点赞数最低
  },
  {
    id: 'vincent',
    nameEn: 'Vincent',
    price: 'paid',
    voteCount: 582  // 文森特排第三
  }
];

const ReaderSelection: React.FC = () => {
  const { navigate } = useLanguageNavigate();
  const location = useLocation();
  const { t, i18n } = useTranslation();
  const { user } = useUser();
  const { theme } = useTheme();
  const isDark = theme === 'dark';
  const [showVipPrompt, setShowVipPrompt] = useState(false);
  const [showLoginPrompt, setShowLoginPrompt] = useState(false);
  const [, setHasUsedRaven] = useState(true); // 默认为已使用过，变量未使用但保留setter函数
  const [readers, setReaders] = useState<Reader[]>(defaultReaders);
  const [playingReaderId, setPlayingReaderId] = useState<string | null>(null);
  const [audioElements, setAudioElements] = useState<{[key: string]: HTMLAudioElement}>({});
  const [isVoting, setIsVoting] = useState<{[key: string]: boolean}>({});
  const [loadingAudio, setLoadingAudio] = useState<{[key: string]: boolean}>({});
  
  // 获取当前语言
  const currentLanguage = i18n.language;
  // 允许在所有语言环境下显示试听按钮
  const showPreviewButton = true; // 移除语言限制，在所有语言下都显示试听按钮

  // 试听示例文本 - 根据语言选择不同的示例文本
  const getSampleText = () => {
    // 获取语言代码 (zh, en, ja)
    const langCode = currentLanguage.split('-')[0];
    
    switch(langCode) {
      case 'en':
        return "Welcome to the world of Tarot. Let me guide you through the mysteries of destiny.";
      case 'ja':
        return "タロットの世界へようこそ。運命の神秘をご案内します。";
      case 'zh':
      default:
        return "欢迎来到塔罗世界，让我为您解读命运的奥秘。";
    }
  };
  
  const sampleText = getSampleText();

  useEffect(() => {
    // 使用类控制滚动而不是直接设置overflow样式
    document.body.classList.add('no-scroll');
    
    // 检查用户是否使用过渡鸦
    const checkRavenUsage = async () => {
      try {
        // 只有在用户已登录的情况下才检查
        let ravenUsed = true; // 默认为已使用
        if (user) {
          ravenUsed = await checkRavenUsed();
        }
        setHasUsedRaven(ravenUsed);
        
        // 查找渡鸦和基础占卜师
        const basicReader = defaultReaders.find(r => r.id === 'basic');
        const ravenReader = defaultReaders.find(r => r.id === 'raven');
        const otherReaders = defaultReaders.filter(r => r.id !== 'basic' && r.id !== 'raven');
        
        // 确保基础占卜师始终存在
        let updatedReaders: Reader[] = [];
        
        // 根据是否使用过渡鸦来排序和修改readers数组
        if (!ravenUsed) {
          // 对于新用户未使用过渡鸦的情况
          if (basicReader && ravenReader) {
            // 确保基础占卜师(Molly)显示在首位
            updatedReaders = [
              basicReader, // 基础占卜师放在首位
              { ...ravenReader, isFreeFirstTime: true, price: 'free' as const }, // 将渡鸦标记为免费，首次使用
              ...otherReaders
            ];
          } else if (basicReader) {
            updatedReaders = [
              basicReader, // 只有基础占卜师
              ...otherReaders
            ];
          } else if (ravenReader) {
            // 如果没有基础占卜师但有渡鸦
            updatedReaders = [
              { ...ravenReader, isFreeFirstTime: true, price: 'free' as const },
              ...otherReaders
            ];
          } else {
            updatedReaders = [...defaultReaders]; // 使用默认列表
          }
        } else {
          // 已使用过渡鸦的情况
          if (basicReader && ravenReader) {
            updatedReaders = [
              basicReader, // 基础占卜师放在首位
              ravenReader,
              ...otherReaders
            ];
          } else if (basicReader) {
            updatedReaders = [
              basicReader,
              ...otherReaders
            ];
          } else {
            updatedReaders = [...defaultReaders]; // 使用默认列表
          }
        }
        
        // 获取用户已点赞的所有占卜师
        let userVotedReaderIds: string[] = [];
        if (user) {
          try {
            userVotedReaderIds = await getUserVotedReaders();
          } catch (error) {
            // console.error('Error fetching user voted readers:', error);
          }
        }
        
        // 获取每个占卜师的投票数和用户投票状态
        const readersWithVotes = await Promise.all(
          updatedReaders.map(async (reader) => {
            let voteCount;
            try {
              voteCount = await getReaderVotes(reader.id);
            } catch (error) {
              // console.error(`获取占卜师 ${reader.id} 点赞数失败:`, error);
              // 获取失败时使用默认点赞数
              voteCount = reader.voteCount || 0;
            }
            // 根据获取的用户已点赞列表，直接设置hasVoted状态
            const hasVoted = user ? userVotedReaderIds.includes(reader.id) : false;
            return {
              ...reader,
              voteCount,
              hasVoted
            };
          })
        );
        
        setReaders(readersWithVotes);
      } catch (error) {
        // console.error('Error checking raven usage or reader votes:', error);
        // 出错时，确保至少显示基础占卜师
        const basicReader = defaultReaders.find(r => r.id === 'basic');
        if (basicReader) {
          setReaders([basicReader]);
        } else {
          const basicReaders = defaultReaders.filter(r => r.price === 'free');
          setReaders(basicReaders);
        }
      }
    };
    
    checkRavenUsage();
    
    return () => {
      // 移除类，恢复滚动
      document.body.classList.remove('no-scroll');
      
      // 清理所有音频资源
      Object.values(audioElements).forEach(audio => {
        audio.pause();
        audio.src = '';
      });
      
      // 在组件卸载时，自动提交所有待处理的点赞记录
      const submitVotes = async () => {
        try {
          // 检查是否有待处理的点赞记录
          const pendingCount = getPendingVotesCount();
          if (pendingCount > 0 && user) {
            await submitPendingVotes();
          }
        } catch (error) {
        }
      };
      
      // 执行提交操作
      submitVotes();
    };
  }, [audioElements, user]);

  // 添加路由变化时停止所有音频的功能
  useEffect(() => {
    // 返回清理函数，在组件卸载或路由变化时执行
    return () => {
      // 停止所有正在播放的音频
      Object.values(audioElements).forEach(audio => {
        if (audio && !audio.paused) {
          audio.pause();
          audio.currentTime = 0;
        }
      });
      // 清空正在播放的状态
      setPlayingReaderId(null);
    };
  }, [location.pathname, audioElements]); // 监听路由变化和音频元素变化

  // 统一的权限检查函数
  const checkUserPermission = () => {
    if (!user) {
      // 显示登录提示弹窗而不是toast
      setShowLoginPrompt(true);
      return false;
    }
    if (user.vipStatus === 'active') return true;
    if (user.remainingReads <= 0) {
      setShowVipPrompt(true);
      return false;
    }
    return true;
  };

  const handleReaderSelect = async (reader: Reader) => {
    // 先检查基本权限
    if (!checkUserPermission()) {
      return;
    }

    // 如果不是首次免费的渡鸦，并且是付费塔罗师，并且用户不是VIP，显示VIP提示弹窗
    if (reader.price === 'paid' && !reader.isFreeFirstTime && user?.vipStatus !== 'active') {
      setShowVipPrompt(true);
      return;
    }
    
    // 先保存到本地存储，包含翻译后的名字和类型
    localStorage.setItem('selectedReader', JSON.stringify({
      ...reader,
      name: t(`reader.${reader.id}.name`),
      type: t(`reader.${reader.id}.type`)
    }));
    
    try {
      // 更新会话信息
      const sessionId = localStorage.getItem('sessionId');
      if (sessionId) {
        await updateSession(sessionId, {
          selectedReader: {
            id: reader.id,
            name: t(`reader.${reader.id}.name`),
            type: reader.isFreeFirstTime ? 'free_first_time' : (reader.price === 'paid' ? 'vip' : 'free')
          },
          status: 'reader_selected'
        }, false).catch(() => {
          // console.error('Error updating session with reader:', error);
        });
      }
      
      // 直接导航到下一页，不添加显示加载状态
      navigate('/reading/spread');
    } catch (err) {
      // console.error('预加载资源出错:', err);
      // 出错时也要导航到下一页
      navigate('/reading/spread');
    }
  };

  // 处理点赞/取消点赞
  const handleVote = async (reader: Reader, event: React.MouseEvent) => {
    event.preventDefault();
    event.stopPropagation();
    
    // 如果用户未登录，显示登录提示弹窗
    if (!user) {
      setShowLoginPrompt(true);
      return;
    }
    
    // 如果该占卜师正在进行投票操作，忽略本次点击
    if (isVoting[reader.id]) {
      return;
    }
    
    try {
      // 设置投票状态为处理中
      setIsVoting(prev => ({ ...prev, [reader.id]: true }));
      
      // 确保我们有准确的当前状态
      const currentVoted = reader.hasVoted || false;
      const newVoteCount = currentVoted ? (reader.voteCount || 0) - 1 : (reader.voteCount || 0) + 1;
      
      // 切换本地缓存状态，传入当前服务器状态
      toggleReaderVoteLocal(reader.id, currentVoted);
      
      // 确保结果状态是新的（与当前状态相反）
      const newVotedState = !currentVoted;
      
      // 更新readers数组中当前占卜师的投票状态和数量
      setReaders(prevReaders => 
        prevReaders.map(r => 
          r.id === reader.id 
            ? { ...r, voteCount: newVoteCount, hasVoted: newVotedState }
            : r
        )
      );
      
      // 显示操作结果提示，添加根据主题适配的样式
      if (newVotedState) {
        toast.success(t('reader.vote.success'), {
          style: {
            background: isDark ? 'rgba(51, 65, 85, 0.95)' : 'rgba(255, 255, 255, 0.95)',
            color: isDark ? '#FFFFFF' : '#1F2937',
            border: isDark ? '1px solid rgba(148, 163, 184, 0.2)' : '1px solid rgba(168, 85, 247, 0.2)',
          }
        });
      } else {
        toast.success(t('reader.vote.canceled'), {
          style: {
            background: isDark ? 'rgba(51, 65, 85, 0.95)' : 'rgba(255, 255, 255, 0.95)',
            color: isDark ? '#FFFFFF' : '#1F2937',
            border: isDark ? '1px solid rgba(148, 163, 184, 0.2)' : '1px solid rgba(168, 85, 247, 0.2)',
          }
        });
      }
      
    } catch (error) {
      // 显示错误提示，同样添加主题适配
      toast.error(t('reader.vote.error'), {
        style: {
          background: isDark ? 'rgba(51, 65, 85, 0.95)' : 'rgba(255, 255, 255, 0.95)',
          color: isDark ? '#FFFFFF' : '#1F2937',
          border: isDark ? '1px solid rgba(148, 163, 184, 0.2)' : '1px solid rgba(168, 85, 247, 0.2)',
        }
      });
    } finally {
      // 重置投票状态
      setIsVoting(prev => ({ ...prev, [reader.id]: false }));
    }
  };

  const getPriceLabel = (reader: Reader): PriceLabel => {
    if (reader.isFreeFirstTime) {
      return { text: t('reader.price.free_first_time', '首次免费'), className: 'bg-purple-600/70 text-white text-xs tracking-wider px-2 py-0.5 rounded' };
    }
    
    switch (reader.price) {
      case 'free':
        return { text: t('reader.price.free'), className: 'bg-green-500/20 text-green-400 text-xs tracking-wider px-2 py-0.5 rounded' };
      case 'paid':
        return { text: t('reader.price.vip'), className: 'bg-amber-500 font-medium text-xs tracking-wider px-2 py-0.5 rounded', style: { color: '#000' } };
      default:
        return { text: '', className: '' };
    }
  };

  const getButtonText = (reader: Reader) => {
    if (reader.isFreeFirstTime) {
      return t('reader.button.free_first_time');
    }
    
    if (reader.price === 'free' || user?.vipStatus === 'active') {
      return t('reader.button.select');
    }
    
    return t('reader.button.upgrade');
  };

  // 处理试听功能
  const handlePreviewVoice = async (reader: Reader) => {
    try {
      // 如果正在播放同一个读者的声音，则停止播放
      if (playingReaderId === reader.id) {
        if (audioElements[reader.id]) {
          audioElements[reader.id].pause();
          setPlayingReaderId(null);
        }
        return;
      }

      // 停止其他正在播放的音频
      Object.values(audioElements).forEach(audio => {
        audio.pause();
      });
      
      // 设置当前读者的音频为加载状态
      setLoadingAudio(prev => ({ ...prev, [reader.id]: true }));
      
      // 如果这个读者的音频已经加载过，直接播放
      if (audioElements[reader.id]) {
        audioElements[reader.id].currentTime = 0;
        
        const audio = audioElements[reader.id];
        
        // 设置音频结束事件
        audio.onended = () => {
          setPlayingReaderId(null);
        };
        
        // 尝试播放
        const playPromise = audio.play();
        if (playPromise !== undefined) {
          playPromise.then(() => {
            setPlayingReaderId(reader.id);
            setLoadingAudio(prev => ({ ...prev, [reader.id]: false }));
          }).catch(() => {
            // console.error("播放失败:", error);
            setPlayingReaderId(null);
            setLoadingAudio(prev => ({ ...prev, [reader.id]: false }));
          });
        } else {
          setPlayingReaderId(reader.id);
          setLoadingAudio(prev => ({ ...prev, [reader.id]: false }));
        }
        
        return;
      }
      
      // 获取当前语言代码 (zh, en, ja)
      const langCode = currentLanguage.split('-')[0];

      // 构建messageId，根据不同语言使用不同的格式
      // 对于中文(zh和zh-TW)使用不带语言参数的messageId，对于英文(en)和日文(ja)使用带语言参数的messageId
      const messageId = langCode === 'zh' ? 
        `${reader.id}_para_0` : 
        `${reader.id}_${langCode}_para_0`;
      
      // 从数据库tts_cache表获取音频
      const audio = await fetchParagraphAudio(
        sampleText,
        0,
        reader.id,
        'reader_intro',
        messageId, // 使用根据语言构建的messageId
        reader.id,
        `preview-${reader.id}`,
        langCode // 传递语言参数
      );

      // 设置音频结束事件
      audio.onended = () => {
        setPlayingReaderId(null);
      };

      // 保存音频元素
      setAudioElements(prev => ({
        ...prev,
        [reader.id]: audio
      }));

      // 播放音频
      const playPromise = audio.play();
      if (playPromise !== undefined) {
        playPromise.then(() => {
          setPlayingReaderId(reader.id);
          setLoadingAudio(prev => ({ ...prev, [reader.id]: false }));
        }).catch(() => {
          // console.error("播放失败:", error);
          setPlayingReaderId(null);
          setLoadingAudio(prev => ({ ...prev, [reader.id]: false }));
        });
      } else {
        setPlayingReaderId(reader.id);
        setLoadingAudio(prev => ({ ...prev, [reader.id]: false }));
      }
    } catch (error) {
      // console.error("音频处理错误:", error);
      setPlayingReaderId(null);
      setLoadingAudio(prev => ({ ...prev, [reader.id]: false }));
    }
  };

  return (
    <div className="min-h-screen flex flex-col relative">
      <SEO 
      />
      <LandingBackground />
      <div className="flex-grow relative z-10">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 pb-8 pt-2 sm:pt-4">
          <div className="text-center mt-8 sm:mt-10">
            <h1 className="main-title mb-1">{t('reader.title')}</h1>
            <p className="sub-title mb-4 sm:mb-6">{t('reader.subtitle')}</p>
          </div>
          
          {/* 占卜师卡片网格 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {readers.map((reader) => {
              const priceInfo = getPriceLabel(reader);
              const isPlaying = playingReaderId === reader.id;
              const isAudioLoading = loadingAudio[reader.id];
              
              return (
                <div
                  key={reader.id}
                  className={`relative ${
                    isDark 
                      ? 'bg-black/40' 
                      : 'bg-[#F4F4F5]'
                  } backdrop-blur-sm rounded-xl p-6 ${
                    isDark 
                      ? 'border border-purple-500/20 hover:border-purple-500/40' 
                      : 'border border-purple-300/50 hover:border-purple-400/70'
                  } 
                    transition-all group hover:shadow-lg ${
                      isDark ? 'hover:shadow-purple-500/10' : 'hover:shadow-purple-400/20'
                    } flex flex-col min-h-[280px] font-sans`}
                >
                  <div className="flex items-start space-y-0">
                    <div className="relative w-24 h-24 flex-shrink-0 rounded-full overflow-hidden">
                      <CdnLazyImage
                        src={`/images-optimized/readers/${reader.nameEn}.webp`}
                        alt={t(`reader.${reader.id}.name`)}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="ml-4 flex-1">
                      <div className="flex items-center justify-between mt-2 mb-2">
                        <span className={`text-sm font-medium ${isDark ? 'text-purple-400/90' : 'text-purple-600/90'} tracking-wide uppercase font-sans`}>
                          {t(`reader.${reader.id}.type`)}
                        </span>
                        <span 
                          className={`${priceInfo.className} font-sans whitespace-nowrap`}
                          style={priceInfo.style}
                        >
                          {priceInfo.text}
                        </span>
                      </div>
                      <div className="flex flex-col">
                        <div className="flex items-center gap-2">
                          <h3 className={`text-xl font-medium tracking-wide ${isDark ? 'text-white/90' : 'text-gray-800'} font-sans`}>
                            {t(`reader.${reader.id}.name`)}
                          </h3>
                          {/* 试听按钮，只在中文环境下显示 */}
                          {showPreviewButton && (
                            <button
                              onClick={(e) => {
                                e.preventDefault();
                                handlePreviewVoice(reader);
                              }}
                              disabled={isAudioLoading}
                              className={`px-3 py-1.5 text-sm rounded-md font-medium font-sans flex items-center gap-1.5 shadow-sm
                                ${isDark 
                                  ? isPlaying
                                    ? 'bg-purple-500/30 text-purple-200 ring-1 ring-purple-400/50 shadow-purple-500/30'
                                    : isAudioLoading
                                      ? 'bg-purple-500/20 text-purple-200/70 cursor-wait'
                                      : 'bg-purple-500/20 hover:bg-purple-500/30 text-purple-200 hover:text-purple-100 hover:shadow-purple-500/20 hover:ring-1 hover:ring-purple-400/30'
                                  : isPlaying
                                    ? 'bg-purple-200 text-purple-800 ring-1 ring-purple-300 shadow-purple-200/50'
                                    : isAudioLoading
                                      ? 'bg-purple-100 text-purple-700/70 cursor-wait'
                                      : 'bg-purple-100 hover:bg-purple-200 text-purple-700 hover:text-purple-900 hover:shadow-purple-200/50 hover:ring-1 hover:ring-purple-300'
                                } transition-all duration-200`}
                            >
                              {isPlaying ? (
                                <>
                                  <svg 
                                    className={`w-4 h-4 ${isDark ? 'text-purple-200' : 'text-purple-700'}`}
                                    viewBox="0 0 24 24" 
                                    fill="none" 
                                    stroke="currentColor" 
                                    strokeWidth="2" 
                                    strokeLinecap="round" 
                                    strokeLinejoin="round"
                                  >
                                    <polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5" fill="currentColor" stroke="none"/>
                                    <path d="M15.54 8.46a5 5 0 0 1 0 7.07" />
                                    <path d="M19.07 4.93a10 10 0 0 1 0 14.14" />
                                  </svg>
                                  {t('reader.button.stop_preview', '停止')}
                                </>
                              ) : isAudioLoading ? (
                                <>
                                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-purple-300" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                  </svg>
                                  {t('reader.button.loading')}
                                </>
                              ) : (
                                <>
                                  <svg 
                                    className={`w-4 h-4 ${isDark ? 'text-purple-200' : 'text-purple-700'}`}
                                    viewBox="0 0 24 24" 
                                    fill="none" 
                                    stroke="currentColor" 
                                    strokeWidth="2" 
                                    strokeLinecap="round" 
                                    strokeLinejoin="round"
                                  >
                                    <polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5" fill="currentColor" stroke="none"/>
                                    <path d="M15.54 8.46a5 5 0 0 1 0 7.07" />
                                    <path d="M19.07 4.93a10 10 0 0 1 0 14.14" />
                                  </svg>
                                  {t('reader.button.preview', '试听')}
                                </>
                              )}
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex-1 mt-4">
                    <p className={`${isDark ? 'text-gray-300' : 'text-gray-600'} text-base leading-relaxed font-sans`}>
                      {t(`reader.${reader.id}.description`)}
                    </p>
                  </div>

                  <div className="mt-6 flex items-center justify-between">
                    {/* 选择按钮 */}
                    <button
                      onClick={() => handleReaderSelect(reader)}
                      className={`flex-1 py-3 rounded-lg font-medium font-sans
                        ${reader.isFreeFirstTime
                          ? 'bg-gradient-to-r from-purple-600/90 to-indigo-600/90 hover:from-purple-500 hover:to-indigo-500 text-white shadow-md shadow-purple-500/10'
                          : reader.price === 'paid'
                            ? 'bg-gradient-to-r from-amber-500 to-yellow-500 hover:from-amber-400 hover:to-yellow-400 text-black shadow-lg shadow-amber-500/20' 
                            : 'bg-purple-600 hover:bg-purple-500 text-white'}`}
                    >
                      {getButtonText(reader)}
                    </button>
                    
                    {/* 点赞按钮 */}
                    <button
                      onClick={(e) => handleVote(reader, e)}
                      className={`ml-2 flex items-center justify-center w-12 h-12 rounded-full 
                        ${reader.hasVoted 
                          ? isDark 
                            ? 'bg-red-500/90 text-white shadow-lg shadow-red-500/20' 
                            : 'bg-red-500 text-white shadow-lg shadow-red-500/20'
                          : isDark 
                            ? 'bg-gray-800/80 text-gray-400 hover:bg-gray-700/80 hover:text-gray-300' 
                            : 'bg-gray-200 text-gray-500 hover:bg-gray-300 hover:text-gray-600'
                        } transition-all duration-200`}
                      disabled={isVoting[reader.id]}
                      aria-label={reader.hasVoted ? t('reader.vote.remove_vote') : t('reader.vote.add_vote')}
                    >
                      {isVoting[reader.id] ? (
                        <div className="w-5 h-5 border-t-2 border-r-2 border-white rounded-full animate-spin"></div>
                      ) : (
                        <>
                          <svg 
                            className="w-6 h-6" 
                            fill={reader.hasVoted ? "currentColor" : "none"} 
                            stroke="currentColor" 
                            viewBox="0 0 24 24" 
                            strokeWidth={reader.hasVoted ? "0" : "2"}
                          >
                            <path 
                              strokeLinecap="round" 
                              strokeLinejoin="round" 
                              d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" 
                            />
                          </svg>
                          <span className="sr-only">
                            {reader.hasVoted ? t('reader.vote.remove_vote') : t('reader.vote.add_vote')}
                          </span>
                        </>
                      )}
                    </button>
                    
                    {/* 点赞计数 */}
                    <div className={`ml-2 text-sm ${
                      reader.hasVoted 
                        ? isDark ? 'text-red-400' : 'text-red-500'
                        : isDark ? 'text-gray-400' : 'text-gray-500'
                    }`}>
                      {formatNumber(reader.voteCount || 0)}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
      <div className="relative z-10">
        <Footer />
      </div>

      {/* VIP提示弹窗 */}
      <VipPromptDialog 
        isOpen={showVipPrompt} 
        onCancel={() => setShowVipPrompt(false)}
      />
      
      {/* 登录提示弹窗 */}
      <LoginPrompt 
        isOpen={showLoginPrompt} 
        onClose={() => setShowLoginPrompt(false)}
      />
    </div>
  );
};

export default ReaderSelection;
