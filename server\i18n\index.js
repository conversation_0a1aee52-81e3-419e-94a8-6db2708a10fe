const path = require('path');
const fs = require('fs');

// 支持的语言列表
const supportedLanguages = ['zh-CN', 'zh-TW', 'en', 'ja'];
const defaultLanguage = 'zh-CN';

// 加载所有语言文件
const translations = {};

supportedLanguages.forEach(lang => {
  try {
    const filePath = path.join(__dirname, 'locales', `${lang}.json`);
    if (fs.existsSync(filePath)) {
      translations[lang] = JSON.parse(fs.readFileSync(filePath, 'utf8'));
    } else {
      console.warn(`警告: 找不到语言文件 ${filePath}`);
      translations[lang] = {};
    }
  } catch (error) {
    console.error(`加载语言文件 ${lang} 时出错:`, error);
    translations[lang] = {};
  }
});

/**
 * 获取翻译文本
 * @param {string} key - 翻译键
 * @param {string} lang - 语言代码
 * @param {Object} params - 替换参数
 * @returns {string} 翻译后的文本
 */
function translate(key, lang = defaultLanguage, params = {}) {
  // 确保使用支持的语言
  const language = supportedLanguages.includes(lang) ? lang : defaultLanguage;
  
  // 获取翻译
  let translation = translations[language];
  const keys = key.split('.');
  
  // 遍历嵌套键
  for (const k of keys) {
    if (translation && translation[k]) {
      translation = translation[k];
    } else {
      // 如果找不到翻译，尝试使用默认语言
      if (language !== defaultLanguage) {
        let defaultTranslation = translations[defaultLanguage];
        for (const dk of keys) {
          if (defaultTranslation && defaultTranslation[dk]) {
            defaultTranslation = defaultTranslation[dk];
          } else {
            return key; // 如果默认语言也没有，返回键名
          }
        }
        translation = defaultTranslation;
      } else {
        return key; // 如果是默认语言且找不到翻译，返回键名
      }
      break;
    }
  }
  
  // 如果翻译不是字符串，返回键名
  if (typeof translation !== 'string') {
    return key;
  }
  
  // 替换参数
  return translation.replace(/\{\{(\w+)\}\}/g, (match, paramKey) => {
    return params[paramKey] !== undefined ? params[paramKey] : match;
  });
}

/**
 * 创建翻译函数
 * @param {string} lang - 语言代码
 * @returns {Function} 翻译函数
 */
function createTranslator(lang) {
  return (key, params = {}) => translate(key, lang, params);
}

/**
 * 从请求中获取语言
 * @param {Object} req - Express请求对象
 * @returns {string} 语言代码
 */
function getLanguageFromRequest(req) {
  // 尝试从查询参数获取
  if (req.query && req.query.lang && supportedLanguages.includes(req.query.lang)) {
    return req.query.lang;
  }
  
  // 尝试从请求头获取
  if (req.headers['accept-language']) {
    const acceptedLanguages = req.headers['accept-language'].split(',');
    for (const lang of acceptedLanguages) {
      const langCode = lang.split(';')[0].trim();
      if (supportedLanguages.includes(langCode)) {
        return langCode;
      }
      
      // 检查语言前缀匹配
      const prefix = langCode.split('-')[0];
      const matchedLang = supportedLanguages.find(l => l.startsWith(prefix));
      if (matchedLang) {
        return matchedLang;
      }
    }
  }
  
  // 默认返回中文
  return defaultLanguage;
}

/**
 * 国际化中间件
 */
function i18nMiddleware(req, res, next) {
  const lang = getLanguageFromRequest(req);
  req.language = lang;
  req.t = createTranslator(lang);
  
  // 扩展res.json方法，自动翻译消息
  const originalJson = res.json;
  res.json = function(obj) {
    if (obj && obj.message && typeof obj.message === 'string' && obj.message.startsWith('i18n.')) {
      obj.message = translate(obj.message.substring(5), lang);
    }
    return originalJson.call(this, obj);
  };
  
  next();
}

module.exports = {
  translate,
  createTranslator,
  getLanguageFromRequest,
  i18nMiddleware,
  supportedLanguages
}; 