#!/bin/bash

# 检查参数
if [ -z "$1" ]; then
    echo "Usage: ./deploy.sh <version>"
    exit 1
fi

VERSION=$1
DEPLOY_ROOT="/var/www/tarot"
VERSIONS_DIR="$DEPLOY_ROOT/versions"
DEPLOY_DIR="$VERSIONS_DIR/deploy_$VERSION"
DEPLOY_TAR="$VERSIONS_DIR/deploy_$VERSION.tar"
CURRENT_VERSION_FILE="$VERSIONS_DIR/CURRENT_VERSION"
LAST_DEPLOY_TIME_FILE="$VERSIONS_DIR/LAST_DEPLOY_TIME"
DEPLOY_LOG="$VERSIONS_DIR/deploy.log"
# 添加百度推送持久化目录
BAIDU_PUSH_DIR="$DEPLOY_ROOT/baidu_push"
# 添加星座运势持久化目录
HOROSCOPES_DIR="$DEPLOY_ROOT/horoscopes"

# 写入日志的函数
log_message() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "$timestamp - $message" >> "$DEPLOY_LOG"
}

# 确保版本目录和文件存在，并设置正确的权限
mkdir -p "$VERSIONS_DIR"
# 确保百度推送持久化目录存在
mkdir -p "$BAIDU_PUSH_DIR"
# 确保星座运势持久化目录存在
mkdir -p "$HOROSCOPES_DIR"
touch "$DEPLOY_LOG"
touch "$CURRENT_VERSION_FILE"
touch "$LAST_DEPLOY_TIME_FILE"
chmod 666 "$DEPLOY_LOG"
chmod 666 "$CURRENT_VERSION_FILE"
chmod 666 "$LAST_DEPLOY_TIME_FILE"
chmod 755 "$VERSIONS_DIR"
# 设置百度推送目录权限，确保服务进程可以读写
chmod 777 "$BAIDU_PUSH_DIR"
# 设置星座运势目录权限，确保服务进程可以读写
chmod 777 "$HOROSCOPES_DIR"
log_message "Ensuring persistent directories are available: $BAIDU_PUSH_DIR, $HOROSCOPES_DIR"

# 清理所有临时目录的函数
cleanup_temp() {
    echo "Cleaning up temporary files..."
    cd "$VERSIONS_DIR" || return
    rm -rf .tmp_* tmp_*
}

# 先清理所有临时目录
cleanup_temp

# 记录开始部署
log_message "Starting deployment of version $VERSION"

# 检查部署包是否存在
if [ ! -f "$DEPLOY_TAR" ]; then
    log_message "Error: Deployment package not found: $DEPLOY_TAR"
    echo "Deployment package not found: $DEPLOY_TAR"
    exit 1
fi

# 清理旧的部署目录（如果存在）
if [ -d "$DEPLOY_DIR" ]; then
    log_message "Cleaning old deployment directory: $DEPLOY_DIR"
    rm -rf "$DEPLOY_DIR"
fi

# 创建临时解压目录
TMP_DIR="$VERSIONS_DIR/.tmp_$VERSION"
rm -rf "$TMP_DIR"
mkdir -p "$TMP_DIR"

# 解压新版本到临时目录
log_message "Extracting new version to temporary directory"
cd "$VERSIONS_DIR"
if ! tar xf "$DEPLOY_TAR" -C "$TMP_DIR"; then
    log_message "Error: Failed to extract $DEPLOY_TAR"
    cleanup_temp
    exit 1
fi

# 移动文件到正确的位置
log_message "Organizing files..."
if [ ! -d "$TMP_DIR/deploy_$VERSION" ]; then
    log_message "Error: Expected directory structure not found after extraction"
    cleanup_temp
    exit 1
fi

mv "$TMP_DIR/deploy_$VERSION"/* "$TMP_DIR/" || {
    log_message "Error: Failed to move files from deploy_$VERSION"
    cleanup_temp
    exit 1
}

rm -rf "$TMP_DIR/deploy_$VERSION"
mv "$TMP_DIR" "$DEPLOY_DIR" || {
    log_message "Error: Failed to move temporary directory to final location"
    cleanup_temp
    exit 1
}

# 删除tar文件
rm -f "$DEPLOY_TAR"

# 检查目录是否存在
if [ ! -d "$DEPLOY_DIR/server" ]; then
    log_message "Error: server directory not found in deployment package"
    cleanup_temp
    exit 1
fi

if [ ! -d "$DEPLOY_DIR/dist" ]; then
    log_message "Error: dist directory not found in deployment package"
    cleanup_temp
    exit 1
fi

# 安装后端依赖
log_message "Installing backend dependencies..."
cd "$DEPLOY_DIR/server" || {
    log_message "Error: Failed to change directory to server"
    exit 1
}

if ! pnpm install; then
    log_message "Error: Failed to install backend dependencies"
    exit 1
fi

# 直接替换当前版本
log_message "Deploying new version..."
rm -rf "$DEPLOY_ROOT/dist" "$DEPLOY_ROOT/server"
if ! cp -r "$DEPLOY_DIR/dist" "$DEPLOY_ROOT/" || ! cp -r "$DEPLOY_DIR/server" "$DEPLOY_ROOT/"; then
    log_message "Error: Failed to copy new version files"
    exit 1
fi

# 确保生产环境配置正确
log_message "Setting production environment variables..."
cd "$DEPLOY_ROOT/server"
if [ -f ".env" ]; then
    # 备份原始 .env 文件
    cp .env .env.backup
    # 修改 NODE_ENV 为 production
    sed -i 's/NODE_ENV=development/NODE_ENV=production/g' .env
    log_message "Updated NODE_ENV to production in .env file"
else
    log_message "Warning: .env file not found"
fi

# 重启后端服务，使用reload减少不必要的日志轮转
log_message "Reloading backend service..."
cd "$DEPLOY_ROOT/server"

# 检查是否存在 ecosystem.config.js
if [ -f "ecosystem.config.js" ]; then
    log_message "Using ecosystem.config.js with production environment"
    # 先停止现有服务
    pm2 delete tarot-backend 2>/dev/null || true
    # 使用生产环境配置启动
    if ! pm2 start ecosystem.config.js --env production; then
        log_message "Error: Failed to start backend service with ecosystem config"
        exit 1
    fi
else
    log_message "ecosystem.config.js not found, using direct start with environment variables"
    # 先停止现有服务
    pm2 delete tarot-backend 2>/dev/null || true
    # 直接启动并设置环境变量
    if ! NODE_ENV=production pm2 start index.js --name tarot-backend --update-env; then
        log_message "Error: Failed to start backend service"
        exit 1
    fi
fi

# 等待服务启动
sleep 5

# 检查服务状态
echo "Checking service status..."
pm2 status tarot-backend | cat

# 检查服务是否正常运行（改进的检查逻辑）
if pm2 list | grep -q "tarot-backend.*online"; then
    # 更新版本信息
    echo "$VERSION" > "$CURRENT_VERSION_FILE"
    date '+%Y-%m-%d %H:%M:%S' > "$LAST_DEPLOY_TIME_FILE"
    log_message "Successfully deployed version $VERSION"
    echo "Deployment completed successfully!"
else
    log_message "Failed to deploy version $VERSION - Backend service is not running"
    exit 1
fi

# 清理旧版本（保留最近3个版本）
log_message "Cleaning up old versions..."
cd "$VERSIONS_DIR"
ls -t -d deploy_v* | tail -n +4 | xargs -r rm -rf

# 清理临时文件
cleanup_temp

log_message "Deployment process completed" 