import React, { useEffect } from 'react';
import { useUser } from '../contexts/UserContext';
import Footer from '../components/Footer';
import LandingBackground from '../components/LandingBackground';
import { getCurrentUser } from '../services/userService';
import VipUserCard from '../components/VipUserCard';
import { useTranslation } from 'react-i18next';
import SEO from '../components/SEO';
import { checkPrivilegeStatus } from '../services/invitationService';

const UserCard: React.FC = () => {
  const { user, setUser } = useUser();
  const { t, i18n } = useTranslation();

  // 添加获取字体样式的函数
  const getFontClass = () => {
    switch (i18n.language) {
      case 'ja':
        return 'font-sans japanese';
      case 'zh-CN':
      case 'zh-TW':
        return 'font-sans chinese';
      default:
        return 'font-sans';
    }
  };

  // 初始加载和用户数据变化时刷新
  useEffect(() => {
    const refreshUserData = async () => {
      try {
        const userData = await getCurrentUser();
        setUser(userData);
        
        // 检查用户是否有内部权限
        const privilegeStatus = await checkPrivilegeStatus();
        userData.hasInternalPrivilege = privilegeStatus.hasInternalPrivilege;
      } catch (error) {
        // console.error('Failed to refresh user data:', error);
      }
    };

    refreshUserData();
  }, [setUser, user?.username]); // 当用户名变化时重新获取数据

  if (!user) {
    return null;
  }

  // 格式化会员到期时间
  const formatExpiryDate = (expiryDate: string | null) => {
    if (!expiryDate) return 'N/A';
    const date = new Date(expiryDate);
    
    // 根据当前语言选择日期格式
    const locale = i18n.language;
    let formattedDate;
    
    switch(locale) {
      case 'ja':
        formattedDate = date.toLocaleDateString('ja-JP', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });
        break;
      case 'zh-TW':
        formattedDate = date.toLocaleDateString('zh-TW', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });
        break;
      case 'en':
        formattedDate = date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });
        break;
      default:
        formattedDate = date.toLocaleDateString('zh-CN', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });
    }
    
    // 检查是否已过期
    const isExpired = new Date() > date;
    return isExpired ? t('profile.expiry_date_expired', { date: formattedDate }) : formattedDate;
  };

  return (
    <main className="relative">
      <SEO 
      />
      <LandingBackground />
      <div className="min-h-screen flex flex-col">
        <div className="flex-grow relative z-10">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 pb-8">
            <div className="text-center mt-8 sm:mt-10 mb-8 sm:mb-10">
              <h1 className={`main-title mb-1 ${getFontClass()}`}>{t('profile.title')}</h1>
            </div>
            <div className="w-full max-w-lg mx-auto">
              <VipUserCard 
                email={user.email}
                isVip={user.vipStatus === 'active'}
                expiryDate={formatExpiryDate(user.vipEndDate)}
                name={user.username}
                freeReadingsLeft={user.remainingReads}
              />
            </div>
          </div>
        </div>
        <Footer />
      </div>
    </main>
  );
};

export default UserCard; 