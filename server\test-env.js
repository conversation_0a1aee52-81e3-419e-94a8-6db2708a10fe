// 测试环境变量设置
const dotenv = require('dotenv');
const fs = require('fs');

// 加载环境变量
dotenv.config();

console.log('=== 环境变量测试 ===');
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('DEBUG:', process.env.DEBUG);
console.log('PORT:', process.env.PORT);

// 检查是否在生产环境
console.log('=== 环境检测 ===');
console.log('是否存在 /var/www/tarot:', fs.existsSync('/var/www/tarot'));
console.log('当前工作目录:', process.cwd());

// 检查是否在服务器环境（通过检查特定的生产环境标识）
if (fs.existsSync('/var/www/tarot') && !process.env.FORCE_DEV_MODE) {
  console.log('检测到生产环境目录结构，应该设置 NODE_ENV=production');
  process.env.NODE_ENV = 'production';
  console.log('强制设置后的 NODE_ENV:', process.env.NODE_ENV);
} else {
  console.log('未检测到生产环境目录结构，保持当前环境设置');
}

console.log('=== 最终环境设置 ===');
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('是否为生产环境:', process.env.NODE_ENV === 'production');
