const express = require('express');
const cors = require('cors');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const dotenv = require('dotenv');
const nodemailer = require('nodemailer');
const schedule = require('node-schedule');
const { User } = require('./models/User');
const { initializePool } = require('./services/database');
const { trackApiUsage } = require('./middleware/stats');
const EmailService = require('./services/emailService');
const sessionRoutes = require('./routes/session');
const statsRoutes = require('./routes/stats');
const { i18nMiddleware } = require('./i18n');
const path = require('path');
const { initTtsCacheCleanup } = require('./services/ttsCacheCleanup');
const { initVipStatusCleanup } = require('./services/vipStatusCleanup');
const { ReaderVote } = require('./models/ReaderVote');
const { initBaiduUrlPushTask } = require('./tasks/baiduUrlPush');
const fs = require('fs');

// 先加载环境变量
dotenv.config();

// 确保环境变量加载后再初始化邮箱配置
// console.log('正在初始化邮箱配置...');
EmailService.initEmailConfigs();

// 验证邮件配置，但不创建传输器
// console.log('邮件配置已加载，共有 ' + EmailService.emailConfigs.length + ' 个可用邮箱');

// 异步验证所有邮箱配置
(async () => {
  try {
    await EmailService.verifyAllConfigs();
  } catch (error) {
    console.error('验证邮箱配置时出错:', error);
  }
})();

// 初始化TTS缓存清理服务，每小时执行一次（北京时间整点）
initTtsCacheCleanup('0 0 * * * *');

// 初始化VIP状态清理服务，每天执行一次（北京时间午夜）
initVipStatusCleanup('0 0 0 * * *');

// 初始化百度URL推送定时任务
initBaiduUrlPushTask();

// 创建星座运势文件存储目录
const createHoroscopeDirectories = () => {
  // 固定使用生产环境目录
  const horoscopeDir = '/var/www/tarot/horoscopes';
    
  console.log('创建星座运势目录:', horoscopeDir);
  
  const languages = ['zh-CN', 'en', 'ja', 'zh-TW'];
  const types = ['daily', 'weekly', 'monthly', 'yearly', 'love'];
  
  // 确保主目录存在
  if (!fs.existsSync(horoscopeDir)) {
    fs.mkdirSync(horoscopeDir, { recursive: true });
    console.log('创建星座运势主目录:', horoscopeDir);
  }
  
  // 为每种语言和类型创建子目录
  languages.forEach(language => {
    const languageDir = path.join(horoscopeDir, language);
    if (!fs.existsSync(languageDir)) {
      fs.mkdirSync(languageDir, { recursive: true });
      console.log('创建语言目录:', languageDir);
    }
    
    types.forEach(type => {
      const typeDir = path.join(languageDir, type);
      if (!fs.existsSync(typeDir)) {
        fs.mkdirSync(typeDir, { recursive: true });
        console.log('创建运势类型目录:', typeDir);
      }
    });
  });
  
  console.log('星座运势目录结构创建完成');
};

// 创建星座运势目录
createHoroscopeDirectories();

// 初始化星座运势生成定时任务
const { scheduleHoroscopeTasks } = require('./tasks/generateHoroscopes');
scheduleHoroscopeTasks();

// 创建表并初始化服务
const initTables = async () => {
  try {
    // 确保reader_votes表已创建
    await ReaderVote.createTable();
    // console.log('Reader votes table initialized');
  } catch (error) {
    console.error('Error initializing tables:', error);
  }
};

const app = express();

// CORS 配置
app.use(cors({
  origin: process.env.NODE_ENV === 'development' 
    ? function(origin, callback) {
        callback(null, origin); // 允许任何来源访问
      }
    : ['https://tarotqa.com'],
  credentials: true
}));

// 添加安全头部，防止点击劫持攻击
app.use((req, res, next) => {
  // 设置X-Frame-Options头，防止网站被嵌入到iframe中
  res.setHeader('X-Frame-Options', 'SAMEORIGIN');
  // 设置Content-Security-Policy头，提供额外的安全保护
  res.setHeader('Content-Security-Policy', "frame-ancestors 'self'");
  next();
});

// 移除 COOP 和其他可能阻止 postMessage 的安全头
app.use((req, res, next) => {
  res.removeHeader('Cross-Origin-Opener-Policy');
  next();
});

app.use(express.json());
app.use(trackApiUsage);
app.use(i18nMiddleware);

// 提供静态文件服务
app.use(express.static('public'));
app.use('/temp', express.static(path.join(__dirname, 'public/temp')));

// Initialize MySQL connection pool
initializePool()
  .then(() => {
    console.log('Connected to MySQL database');
    // 初始化数据库表
    return initTables();
  })
  .catch(err => {
    console.error('Could not connect to MySQL:', err);
    process.exit(1);
  });

// 输出已加载的邮箱配置信息，不需要重新初始化
// console.log(`已加载 ${EmailService.emailConfigs.length} 个邮箱配置`);

// Import routes
const tarotRouter = require('./routes/tarot');
const readingRouter = require('./routes/reading');
const blogReadingRouter = require('./routes/blog-reading-api');
const spreadRecommendationRouter = require('./routes/spreadRecommendation');
const authRoutes = require('./routes/auth');
const followupRoutes = require('./routes/followup');
const deepAnalysisRoutes = require('./routes/deepanalysis');
const fortuneRoutes = require('./routes/fortune');
const invitationRoutes = require('./routes/invitation');
const userRoutes = require('./routes/user');
const readingFeedbackRoutes = require('./routes/reading-feedback');
const ttsRoutes = require('./routes/tts');
const proTtsRoutes = require('./routes/pro-tts');
const ttsStatisticsRoutes = require('./routes/tts-statistics');
const readerRoutes = require('./routes/reader');
const baiduPushRoutes = require('./routes/baiduPush');
const blogReadingsDataRoutes = require('./routes/blog-readings-data');
const yesNoTarotRoutes = require('./routes/yes-no-tarot');
const horoscopeRoutes = require('./routes/horoscope');
// 删除voice-chat模块导入

// Use routes
app.use('/api/tarot', tarotRouter);
app.use('/api/reading', readingRouter);
app.use('/api/auth', authRoutes);
app.use('/api/session', sessionRoutes);
app.use('/api/stats', statsRoutes);
app.use('/api/followup', followupRoutes);
app.use('/api/payment', require('./src/routes/paymentRoutes'));
app.use('/api/spread-recommendation', spreadRecommendationRouter);
app.use('/api/deep-analysis', deepAnalysisRoutes);
app.use('/api/fortune', fortuneRoutes);
app.use('/api/invitation', invitationRoutes);
app.use('/api/feedback', require('./routes/feedback'));
app.use('/api/user', userRoutes);
app.use('/api/reading-feedback', readingFeedbackRoutes);
app.use('/api/tts', ttsRoutes);
app.use('/api/pro-tts', proTtsRoutes);
app.use('/api/tts-statistics', ttsStatisticsRoutes);
app.use('/api/reader', readerRoutes);
app.use('/api/seo', baiduPushRoutes);
app.use('/api/blog-reading', blogReadingRouter);
app.use('/api/fingerprint', blogReadingsDataRoutes);
app.use('/api/yes-no-tarot', yesNoTarotRoutes);
app.use('/api/horoscope', horoscopeRoutes);
// 删除voice-chat路由配置

// 添加不带/api前缀的TTS路由支持（兼容新的前端请求）
app.use('/tts', ttsRoutes);
app.use('/pro-tts', proTtsRoutes);
app.use('/blog-reading', blogReadingRouter);
app.use('/fingerprint', blogReadingsDataRoutes);
app.use('/auth', authRoutes);  // 添加不带/api前缀的auth路由

// 设置每日统计数据更新和发送邮件任务
const updateDailyStats = async () => {
  const StatsService = require('./services/statsService');
  try {
    console.log('Starting daily stats update...');
    
    // 更新统计数据
    await StatsService.updateUserStats();
    console.log('Daily stats updated successfully');
    
    // 发送统计报告邮件
    await EmailService.sendStatsReport(process.env.ADMIN_EMAILS || process.env.EMAIL_USER);
    console.log('Daily stats report sent successfully');
  } catch (error) {
    console.error('Error in daily stats update:', error);
  }
};

// 每天早上 8:00 运行统计更新和发送邮件
// Cron 格式: '秒 分 时 日 月 星期'
schedule.scheduleJob('0 0 8 * * *', updateDailyStats);

// 启动时也运行一次统计更新（但不发送邮件）
const initializeStats = async () => {
  const StatsService = require('./services/statsService');
  try {
    await StatsService.updateUserStats();
    // console.log('Initial stats update completed');
  } catch (error) {
    console.error('Error in initial stats update:', error);
  }
};

initializeStats();
// 确保公共目录可以被访问
// 固定指向生产环境持久化目录
app.use('/horoscopes', express.static('/var/www/tarot/horoscopes'));

// Start server
const PORT = process.env.PORT || 5000;

const server = app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`Environment: ${process.env.NODE_ENV}`);
  console.log(`Debug mode: ${process.env.DEBUG}`);

  // 设置每日统计数据更新任务
  schedule.scheduleJob('0 0 * * *', updateDailyStats);
});


