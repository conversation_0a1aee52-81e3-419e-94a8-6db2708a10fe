import React, { useEffect, useRef } from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../contexts/ThemeContext';
import CdnLazyImage from '../components/CdnLazyImage';
import LandingBackground from '../components/LandingBackground';
import useRealtimeVoice from '../hooks/useRealtimeVoice';
import '../styles/RealtimeVoiceChat.css';

const RealtimeVoiceChat: React.FC = () => {
  const { t } = useTranslation();
  const { theme } = useTheme();

  // 使用实时语音hook
  const [state, actions] = useRealtimeVoice();

  // Refs
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // 滚动到底部
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [state.messages]);



  return (
    <div className={`realtime-voice-chat ${theme === 'dark' ? 'dark' : ''}`}>
      <Helmet>
        <title>{t('realtimeVoice.pageTitle', '实时语音交互')}</title>
        <meta name="description" content={t('realtimeVoice.pageDescription', '与AI进行实时语音对话，体验自然流畅的语音交互')} />
      </Helmet>

      <LandingBackground />

      <div className="realtime-voice-container">
        {/* 页面标题 */}
        <div className="page-header">
          <h1 className="page-title">{t('realtimeVoice.pageTitle', '实时语音交互')}</h1>
          <p className="page-description">
            {t('realtimeVoice.pageDescription', '与AI进行实时语音对话，体验自然流畅的语音交互')}
          </p>
        </div>

        {!state.isConnected ? (
          // 连接界面
          <div className="connection-screen">
            <div className="connection-content">
              <div className="connection-icon">
                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 15.5c2.21 0 4-1.79 4-4V6c0-2.21-1.79-4-4-4S8 3.79 8 6v5.5c0 2.21 1.79 4 4 4z" fill="currentColor"/>
                  <path d="M19 10v1.5c0 3.87-3.13 7-7 7s-7-3.13-7-7V10H3v1.5c0 4.55 3.4 8.3 7.8 8.9v1.6h-3.3v2h9v-2h-3.3v-1.6c4.4-.6 7.8-4.35 7.8-8.9V10h-2z" fill="currentColor"/>
                </svg>
              </div>
              <h2 className="connection-title">
                {t('realtimeVoice.startConnection', '开始实时语音对话')}
              </h2>
              <p className="connection-description">
                {t('realtimeVoice.connectionDescription', '点击下方按钮连接到AI语音助手，开始自然流畅的语音对话体验。')}
              </p>
              <button
                className="connection-button"
                onClick={actions.connect}
                disabled={state.isConnecting}
              >
                <div className="button-icon">
                  <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M8 5v14l11-7z" fill="currentColor"/>
                  </svg>
                </div>
                {state.isConnecting ? t('realtimeVoice.connecting', '正在连接...') : t('realtimeVoice.connect', '开始连接')}
              </button>
            </div>
          </div>
        ) : (
          // 对话界面
          <div className="chat-interface">
            {/* 对话头部 */}
            <div className="chat-header">
              <div className="chat-header-left">
                <div className="assistant-avatar">
                  <CdnLazyImage src="/images/readers/Vivi.webp" alt="AI Assistant" />
                </div>
                <div className="assistant-info">
                  <h3 className="assistant-name">{t('realtimeVoice.assistantName', 'Vivi')}</h3>
                  <div className={`connection-status ${state.isConnected ? 'connected' : 'disconnected'}`}>
                    <div className="status-dot"></div>
                    <span>{state.isConnected ? t('realtimeVoice.connected', '已连接') : t('realtimeVoice.disconnected', '未连接')}</span>
                  </div>
                </div>
              </div>
              <button className="disconnect-button" onClick={actions.disconnect}>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
                {t('realtimeVoice.disconnect', '断开连接')}
              </button>
            </div>

            {/* 消息区域 */}
            <div className="messages-container">
              <div className="messages-list">
                {state.messages.map((message) => (
                  <div key={message.id} className={`message ${message.isUser ? 'user-message' : 'ai-message'} ${message.type}`}>
                    <div className="message-avatar">
                      <CdnLazyImage
                        src={message.isUser ? "/images/readers/user-avatar.webp" : "/images/readers/Vivi.webp"}
                        alt={message.isUser ? "User" : "AI Assistant"}
                      />
                    </div>
                    <div className="message-content">
                      <div className="message-header">
                        <span className="message-sender">
                          {message.isUser ? t('realtimeVoice.you', '你') : t('realtimeVoice.assistantName', 'Vivi')}
                        </span>
                        <span className="message-time">
                          {message.timestamp.toLocaleTimeString()}
                        </span>
                      </div>
                      <div className="message-text">{message.text}</div>
                    </div>
                  </div>
                ))}
                
                {/* 当前正在输入的消息 */}
                {state.currentUserMessage && (
                  <div className="message user-message typing">
                    <div className="message-avatar">
                      <CdnLazyImage src="/images/readers/user-avatar.webp" alt="User" />
                    </div>
                    <div className="message-content">
                      <div className="message-header">
                        <span className="message-sender">{t('realtimeVoice.you', '你')}</span>
                      </div>
                      <div className="message-text">{state.currentUserMessage}</div>
                    </div>
                  </div>
                )}

                {state.currentAiMessage && (
                  <div className="message ai-message typing">
                    <div className="message-avatar">
                      <CdnLazyImage src="/images/readers/Vivi.webp" alt="AI Assistant" />
                    </div>
                    <div className="message-content">
                      <div className="message-header">
                        <span className="message-sender">{t('realtimeVoice.assistantName', 'Vivi')}</span>
                      </div>
                      <div className="message-text">{state.currentAiMessage}</div>
                    </div>
                  </div>
                )}
                
                <div ref={messagesEndRef} />
              </div>
            </div>

            {/* 控制面板 */}
            <div className="control-panel">
              <div className="audio-visualizer">
                <div className="audio-level-container">
                  <div
                    className="audio-level-bar"
                    style={{ width: `${state.audioLevel}%` }}
                  ></div>
                </div>
                <div className="status-text">
                  {state.isRecording
                    ? t('realtimeVoice.recording', '正在录音...')
                    : state.isProcessing
                      ? t('realtimeVoice.processing', '正在处理...')
                      : state.isPlaying
                        ? t('realtimeVoice.playing', '正在播放...')
                        : t('realtimeVoice.ready', '准备就绪')}
                </div>
              </div>
              
              <div className="control-buttons">
                <button
                  className={`control-button microphone-button ${!state.microphoneEnabled ? 'disabled' : ''}`}
                  onClick={actions.toggleMicrophone}
                  title={state.microphoneEnabled ? t('realtimeVoice.muteMicrophone', '静音麦克风') : t('realtimeVoice.unmuteMicrophone', '取消静音')}
                >
                  <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    {state.microphoneEnabled ? (
                      <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z" fill="currentColor"/>
                    ) : (
                      <>
                        <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z" fill="currentColor"/>
                        <line x1="1" y1="1" x2="23" y2="23" stroke="currentColor" strokeWidth="2"/>
                      </>
                    )}
                  </svg>
                </button>

                <button
                  className={`control-button record-button ${state.isRecording ? 'recording' : ''}`}
                  onClick={actions.toggleRecording}
                  disabled={!state.microphoneEnabled}
                  title={state.isRecording ? t('realtimeVoice.stopRecording', '停止录音') : t('realtimeVoice.startRecording', '开始录音')}
                >
                  <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    {state.isRecording ? (
                      <rect x="6" y="6" width="12" height="12" fill="currentColor"/>
                    ) : (
                      <circle cx="12" cy="12" r="8" fill="currentColor"/>
                    )}
                  </svg>
                </button>
                
                <button 
                  className="control-button volume-button"
                  title={t('realtimeVoice.volumeControl', '音量控制')}
                >
                  <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z" fill="currentColor"/>
                  </svg>
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default RealtimeVoiceChat;
