import { useCallback, useEffect } from 'react';
import axios from '../utils/axios';
import { useTranslation } from 'react-i18next';
import { useParams, useLocation } from 'react-router-dom';

type HoroscopeType = 'daily' | 'weekly' | 'monthly' | 'yearly' | 'love';

// 星座运势数据类型
export interface HoroscopeData {
  sign: string;
  type: string;
  date: string;
  content: string;
  language: string;
  generated_at: string;
}

// 获取月初日期的辅助函数
const getStartOfMonth = (date: Date): Date => {
  const d = new Date(date);
  d.setDate(1);
  return d;
};

// 获取年初日期的辅助函数
const getStartOfYear = (date: Date): Date => {
  const d = new Date(date);
  d.setMonth(0); // 设置为1月
  d.setDate(1); // 设置为1号
  return d;
};

// 获取周一日期的辅助函数（一周的第一天）
const getStartOfWeek = (date: Date): Date => {
  const d = new Date(date);
  const day = d.getDay();
  const diff = d.getDate() - day + (day === 0 ? -6 : 1); // 调整为周一为一周的第一天
  return new Date(d.setDate(diff));
};

// 全局请求状态，避免并发请求
const pendingRequests: { [key: string]: boolean } = {};

// localStorage缓存操作的工具函数
const localStorageCache = {
  // 缓存前缀，用于区分不同类型的缓存
  prefix: 'horoscope_cache_',
  
  // 缓存有效期（毫秒）
  expireTime: 1000 * 60 * 60 * 24, // 24小时
  
  // 获取缓存
  get: (key: string): HoroscopeData | null => {
    try {
      const item = localStorage.getItem(`${localStorageCache.prefix}${key}`);
      if (!item) return null;
      
      const parsed = JSON.parse(item);
      
      // 检查缓存是否过期
      if (Date.now() - parsed.timestamp > localStorageCache.expireTime) {
        localStorage.removeItem(`${localStorageCache.prefix}${key}`);
        return null;
      }
      
      return parsed.data;
    } catch (error) {
      return null;
    }
  },
  
  // 设置缓存
  set: (key: string, data: HoroscopeData) => {
    try {
      const item = {
        data,
        timestamp: Date.now()
      };
      localStorage.setItem(`${localStorageCache.prefix}${key}`, JSON.stringify(item));
    } catch (error) {
    }
  },
  
  // 清除所有缓存
  clear: () => {
    try {
      Object.keys(localStorage).forEach(key => {
        if (key.startsWith(localStorageCache.prefix)) {
          localStorage.removeItem(key);
        }
      });
    } catch (error) {
    }
  },
  
  // 清除过期缓存
  clearExpired: () => {
    try {
      Object.keys(localStorage).forEach(key => {
        if (key.startsWith(localStorageCache.prefix)) {
          try {
            const item = JSON.parse(localStorage.getItem(key) || '');
            if (Date.now() - item.timestamp > localStorageCache.expireTime) {
              localStorage.removeItem(key);
            }
          } catch (e) {
            // 如果解析失败，直接删除
            localStorage.removeItem(key);
          }
        }
      });
    } catch (error) {
    }
  }
};

/**
 * 自定义Hook，用于获取星座运势数据
 * @param type 运势类型
 * @returns 包含星座运势数据的对象和加载状态
 */
export const useHoroscopeCache = (type: HoroscopeType) => {
  const { i18n } = useTranslation();
  const { lang } = useParams<{ lang: string }>();
  const location = useLocation();

  // 获取当前语言，优先从URL参数获取，然后从i18n获取
  const getCurrentLanguage = useCallback(() => {
    // 如果URL中有语言参数，使用URL中的语言
    if (lang && ['zh-CN', 'zh-TW', 'en', 'ja'].includes(lang)) {
      return lang;
    }

    // 尝试从路径中提取语言
    const pathMatch = location.pathname.match(/^\/([a-z]{2}(-[A-Z]{2})?)/);
    if (pathMatch && ['zh-CN', 'zh-TW', 'en', 'ja'].includes(pathMatch[1])) {
      return pathMatch[1];
    }

    // 最后使用i18n的语言设置
    return i18n.language;
  }, [lang, location.pathname, i18n.language]);

  // 组件挂载时清理过期缓存
  useEffect(() => {
    localStorageCache.clearExpired();
  }, []);

  // 获取特定星座的运势数据
  const getHoroscopeForSign = useCallback(async (signId: string): Promise<HoroscopeData | null> => {
    // 直接调用getHoroscopeForSignWithDate获取当前日期的数据
    const today = new Date();
    return await getHoroscopeForSignWithDate(signId, today);
  }, []);

  // 获取特定日期特定星座的运势数据
  const getHoroscopeForSignWithDate = useCallback(async (signId: string, date: Date): Promise<HoroscopeData | null> => {
    // 根据不同类型的运势调整日期
    let targetDate = date;
    if (type === 'monthly' || type === 'love') {
      targetDate = getStartOfMonth(date);
    } else if (type === 'yearly') {
      targetDate = getStartOfYear(date);
    } else if (type === 'weekly') {
      targetDate = getStartOfWeek(date);
    }
    
    const dateStr = targetDate.toISOString().split('T')[0];
    const language = getCurrentLanguage();
    const cacheKey = `${type}_${dateStr}_${language}_${signId}`;

    // 检查localStorage缓存
    const cachedData = localStorageCache.get(cacheKey);
    if (cachedData) {
    //   console.log(`从localStorage缓存中找到数据: ${signId} ✓`);
      return cachedData;
    }
    
    // 检查是否已有正在进行的请求
    if (pendingRequests[cacheKey]) {
    //   console.log(`已有相同请求正在进行中，等待结果: ${signId}`);
      
      // 等待请求完成
      let retryCount = 0;
      while (pendingRequests[cacheKey] && retryCount < 10) {
        await new Promise(resolve => setTimeout(resolve, 200));
        retryCount++;
      }
      
      // 检查请求完成后是否有数据
      const dataAfterWait = localStorageCache.get(cacheKey);
      if (dataAfterWait) {
        // console.log(`等待后从localStorage缓存中找到数据: ${signId} ✓`);
        return dataAfterWait;
      }
    }
    
    // 直接从API获取数据
    const apiUrl = `/api/horoscope/${signId}/${type}`;
    // console.log(`🚀 调用API: ${apiUrl}?date=${dateStr}&language=${language}`);
    
    // 标记请求开始
    pendingRequests[cacheKey] = true;
    
    try {
      const response = await axios.get(apiUrl, {
        params: {
          date: dateStr,
          language
        }
      });
      
      if (response.data) {
        
        // 更新localStorage缓存
        localStorageCache.set(cacheKey, response.data);
        
        return response.data;
      } else {
        return null;
      }
    } catch (error) {
      return null;
    } finally {
      // 标记请求结束
      pendingRequests[cacheKey] = false;
    }
  }, [type, getCurrentLanguage]);

  // 清除缓存的方法
  const clearCache = useCallback(() => {
    localStorageCache.clear();
  }, []);

  return { 
    loading: false, 
    error: null, 
    data: null, 
    getHoroscopeForSign, 
    getHoroscopeForSignWithDate, 
    dateSpecificLoading: {},
    clearCache
  };
};

export default useHoroscopeCache; 