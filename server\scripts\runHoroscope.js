const path = require('path');
// 修改环境变量文件路径，指向 server/.env
const dotenvPath = path.join(__dirname, '../.env');
require('dotenv').config({ path: dotenvPath });



// 导入星座运势生成函数
const { HOROSCOPE_TYPES, ZODIAC_SIGNS, getHoroscope, formatDateForDb } = require('../services/horoscopeService');
const { getAllHoroscopesFromFile, getSignHoroscopeFromFile, cleanupOldFiles } = require('../services/horoscopeFileService');
const { ensureHoroscopeTableExists } = require('../services/horoscopeDbService');
const EmailService = require('../services/emailService');
const fs = require('fs').promises;

// 获取命令行参数
const args = process.argv.slice(2);
const type = args[0]; // 运势类型: daily, weekly, monthly, yearly, love 或 all
const language = args[1] || 'zh-CN'; // 语言: zh-CN, en, ja, zh-TW
const delay = parseInt(args[2] || '3000', 10); // 延迟时间(毫秒)，默认3秒

/**
 * 打印分割线
 * @param {string} message 分割线中的消息
 */
function printDivider(message = '') {
  const dividerLength = 60;
  const messageWithSpaces = message ? ` ${message} ` : '';
  const sideLength = Math.max(0, (dividerLength - messageWithSpaces.length) / 2);
  const leftSide = '='.repeat(Math.floor(sideLength));
  const rightSide = '='.repeat(Math.ceil(sideLength));
  console.log(`\n${leftSide}${messageWithSpaces}${rightSide}\n`);
}

/**
 * 延迟执行函数
 * @param {number} ms 毫秒数
 * @returns {Promise<void>}
 */
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 检查星座运势生成结果，如果有失败的情况则输出到控制台
 * @param {string} type 运势类型
 * @param {Date} date 日期
 * @param {string} language 语言
 * @returns {Promise<boolean>} 是否全部成功
 */
async function checkHoroscopeGeneration(type, date, language) {
  try {
    const dateStr = date.toISOString().split('T')[0];
    // 删除重复的日志输出，因为已经在generateHoroscopesForAllSigns函数中输出了
    
    // 从本地文件获取所有星座的运势数据
    const horoscopes = await getAllHoroscopesFromFile(type, date, language);
    
    // 检查是否所有星座都有数据
    const missingZodiacs = [];
    for (const sign of ZODIAC_SIGNS) {
      if (!horoscopes || !horoscopes[sign]) {
        missingZodiacs.push(sign);
      }
    }
    
          // 如果有缺失的星座数据，输出到控制台并发送邮件
    if (missingZodiacs.length > 0) {
      console.warn(`${language} ${type} ${dateStr} 运势生成不完整，缺少以下星座: ${missingZodiacs.join(', ')}`);
      
      // 获取管理员邮箱，使用ADMIN_EMAILS环境变量
      const adminEmails = process.env.ADMIN_EMAILS || '<EMAIL>';
      
      // 构建邮件内容
      const formattedDate = formatDateForDb(date);
      const emailSubject = `星座运势生成失败提醒 - ${language} ${type} ${formattedDate}`;
      const emailHtml = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
          <h2 style="color: #d9534f; margin-top: 0;">星座运势生成失败提醒</h2>
          <p>系统检测到部分星座运势生成失败，详情如下：</p>
          
          <div style="margin: 20px 0; padding: 15px; background-color: #f8f9fa; border-radius: 4px;">
            <p><strong>运势类型:</strong> ${type}</p>
            <p><strong>语言:</strong> ${language}</p>
            <p><strong>目标日期:</strong> ${formattedDate}</p>
            <p><strong>缺失星座:</strong> ${missingZodiacs.join(', ')}</p>
          </div>
          
          <p>请检查API调用日志和本地文件，确认失败原因并手动重新生成。</p>
          
          <hr style="margin: 20px 0; border: 0; border-top: 1px solid #eee;">
          <p style="color: #777; font-size: 12px;">此邮件由系统自动发送，请勿直接回复。</p>
        </div>
      `;
      
      // 发送提醒邮件
      try {
        console.log(`正在发送提醒邮件至 ${adminEmails}...`);
        await EmailService.sendMailWithRetry({
          to: adminEmails,
          subject: emailSubject,
          html: emailHtml
        });
        console.log(`已发送提醒邮件至 ${adminEmails}`);
      } catch (emailError) {
        console.error('发送提醒邮件失败:', emailError);
      }
      
      return false;
    }
    
    console.log(`${language} ${type} ${dateStr} 所有星座运势生成成功!`);
    return true;
  } catch (error) {
    console.error(`检查星座运势生成结果失败:`, error);
    return false;
  }
}

/**
 * 检查文件是否已存在
 * @param {string} type 运势类型
 * @param {string} sign 星座ID
 * @param {Date} date 日期
 * @param {string} language 语言
 * @returns {Promise<boolean>} 是否存在
 */
async function checkFileExists(type, sign, date, language) {
  try {
    const fileData = await getSignHoroscopeFromFile(sign, type, date, language);
    return !!fileData;
  } catch (error) {
    return false;
  }
}

/**
 * 为指定类型生成运势
 * @param {string} type 运势类型
 * @param {Date} date 日期
 * @param {string} language 语言
 * @returns {Promise<boolean>} 是否成功
 */
async function generateHoroscopesForAllSigns(type, date, language) {
  try {
    const dateStr = date.toISOString().split('T')[0];

    
    // 检查文件是否已存在
    const fileExists = await checkFileExists(type, ZODIAC_SIGNS[0], date, language);
    
    if (fileExists) {
      console.log(`找到已存在的文件: ${language} ${type} ${dateStr}`);
    } else {
      console.log(`文件不存在，开始生成: ${language} ${type} ${dateStr}`);
    }
    
    // 只需调用一次getHoroscope，它会生成并保存所有星座的运势
    // 使用第一个星座作为参数，但实际上会生成所有星座的数据
    const horoscopeResult = await getHoroscope(type, ZODIAC_SIGNS[0], date, language);
    
    if (!fileExists) {
      console.log(`成功生成 ${language} ${type} ${dateStr} 所有星座运势`);
      if (horoscopeResult.input_tokens && horoscopeResult.output_tokens) {
        console.log(`使用Token情况: 输入Token: ${horoscopeResult.input_tokens}, 输出Token: ${horoscopeResult.output_tokens}`);
      }
    }
    
    // 添加检查逻辑，验证所有星座的数据是否都已成功生成
    await checkHoroscopeGeneration(type, date, language);
    
    return true;
  } catch (error) {
    console.error(`❌ 生成 ${language} ${type} 运势失败:`, error);
    return false;
  }
}

/**
 * 获取指定类型的最近三个日期的运势文件
 * @param {string} type 运势类型
 * @param {string} language 语言
 * @returns {Promise<Array<Date>>} 日期数组
 */
async function getLatestThreeDates(type, language) {
  try {
    // 确定存储路径
    const basePath = path.join(__dirname, '..', 'public', 'horoscopes', language, type);
    
    // 检查目录是否存在
    try {
      await fs.access(basePath);
    } catch (error) {
      console.log(`目录不存在: ${basePath}`);
      return [];
    }
    
    // 获取目录中的所有文件
    const files = await fs.readdir(basePath);
    
    // 过滤出JSON文件并解析日期
    const dates = files
      .filter(file => file.endsWith('.json'))
      .map(file => {
        const dateStr = file.replace('.json', '');
        return new Date(dateStr);
      })
      .filter(date => !isNaN(date.getTime())) // 过滤掉无效日期
      .sort((a, b) => b - a); // 按日期降序排序
    
    // 返回最近的三个日期
    return dates.slice(0, 3);
  } catch (error) {
    console.error(`获取最近三个日期失败:`, error);
    return [];
  }
}

/**
 * 为指定类型生成最近三个日期的运势
 * @param {string} type 运势类型
 * @param {string} language 语言
 * @returns {Promise<void>}
 */
async function generateLatestThreeForType(type, language) {
  printDivider(`${language} ${type}`);
  
  // 根据运势类型确定日期
  let dates = [];
  const today = new Date();
  
  if (type === HOROSCOPE_TYPES.DAILY) {
    // 日运：昨天、今天、明天（上个周期、本周期、下个周期）
    // 使用UTC日期，避免时区问题
    const yesterday = new Date(Date.UTC(today.getUTCFullYear(), today.getUTCMonth(), today.getUTCDate() - 1));
    const todayUTC = new Date(Date.UTC(today.getUTCFullYear(), today.getUTCMonth(), today.getUTCDate()));
    const tomorrow = new Date(Date.UTC(today.getUTCFullYear(), today.getUTCMonth(), today.getUTCDate() + 1));
    
    dates = [yesterday, todayUTC, tomorrow];
  } else if (type === HOROSCOPE_TYPES.WEEKLY) {
    // 周运：上周、本周、下周（上个周期、本周期、下个周期）
    // 计算本周一的日期
    const currentDay = today.getUTCDay(); // 0是周日，1是周一
    const daysToMonday = currentDay === 0 ? -6 : 1 - currentDay; // 计算到本周一的天数
    
    // 创建上周一、本周一、下周一的日期对象（使用UTC时间）
    const thisMonday = new Date(Date.UTC(today.getUTCFullYear(), today.getUTCMonth(), today.getUTCDate() + daysToMonday));
    const lastMonday = new Date(Date.UTC(thisMonday.getUTCFullYear(), thisMonday.getUTCMonth(), thisMonday.getUTCDate() - 7));
    const nextMonday = new Date(Date.UTC(thisMonday.getUTCFullYear(), thisMonday.getUTCMonth(), thisMonday.getUTCDate() + 7));
    
    dates = [
      lastMonday,
      thisMonday,
      nextMonday
    ];
  } else if (type === HOROSCOPE_TYPES.MONTHLY) {
    // 月运：上月、本月、下月（上个周期、本周期、下个周期）
    // 创建上个月、本月、下个月的日期对象（使用UTC时间）
    const thisMonth = new Date(Date.UTC(today.getUTCFullYear(), today.getUTCMonth(), 1));
    const lastMonth = new Date(Date.UTC(today.getUTCFullYear(), today.getUTCMonth() - 1, 1));
    const nextMonth = new Date(Date.UTC(today.getUTCFullYear(), today.getUTCMonth() + 1, 1));
    
    dates = [
      lastMonth,
      thisMonth,
      nextMonth
    ];
  } else if (type === HOROSCOPE_TYPES.YEARLY) {
    // 年运：去年、今年、明年（上个周期、本周期、下个周期）
    // 创建去年、今年、明年的日期对象（使用UTC时间）
    const thisYear = new Date(Date.UTC(today.getUTCFullYear(), 0, 1));
    const lastYear = new Date(Date.UTC(today.getUTCFullYear() - 1, 0, 1));
    const nextYear = new Date(Date.UTC(today.getUTCFullYear() + 1, 0, 1));
    
    dates = [
      lastYear,
      thisYear,
      nextYear
    ];
  } else if (type === HOROSCOPE_TYPES.LOVE) {
    // 爱情运势：上月、本月、下月（上个周期、本周期、下个周期）
    // 创建上个月、本月、下个月的日期对象（使用UTC时间）
    const thisMonth = new Date(Date.UTC(today.getUTCFullYear(), today.getUTCMonth(), 1));
    const lastMonth = new Date(Date.UTC(today.getUTCFullYear(), today.getUTCMonth() - 1, 1));
    const nextMonth = new Date(Date.UTC(today.getUTCFullYear(), today.getUTCMonth() + 1, 1));
    
    dates = [
      lastMonth,
      thisMonth,
      nextMonth
    ];
  }
  
  // 为每个日期生成运势
  for (const date of dates) {
    const dateStr = date.toISOString().split('T')[0];
    await generateHoroscopesForAllSigns(type, date, language);
    
    // 添加延迟，避免API调用过于频繁
    await sleep(delay);
  }
  
  // 手动清理旧文件，确保只保留最新的三个日期
  try {
    console.log(`清理 ${language} ${type} 旧文件...`);
    await cleanupOldFiles(type, language);
    console.log(`${language} ${type} 旧文件清理完成`);
  } catch (error) {
    console.error(`清理 ${language} ${type} 旧文件失败:`, error);
  }
  
  console.log(`${language} ${type} 运势生成完成！`);
}

/**
 * 主函数
 */
async function main() {
  try {
    printDivider('正在初始化星座运势数据库表结构');
    
    // 确保数据库表结构正确
    try {
      await ensureHoroscopeTableExists();
      console.log('数据库表结构已准备就绪');
    } catch (dbError) {
      console.error('初始化数据库表结构失败:', dbError);
      console.log('继续执行，数据将只保存到文件...');
    }
    
    // 根据命令行参数确定要生成的运势类型和语言
    const typeArg = args[0]?.toLowerCase();
    const languageArg = args[1];
    
    // 确定要处理的语言列表
    const languages = languageArg ? [languageArg] : ['zh-CN', 'en', 'ja', 'zh-TW'];
    
    // 确定要处理的运势类型列表
    let types = [];
    if (!typeArg || typeArg === 'all') {
      // 如果没有指定类型或指定为'all'，则处理所有类型
      types = [
        HOROSCOPE_TYPES.DAILY,
        HOROSCOPE_TYPES.WEEKLY,
        HOROSCOPE_TYPES.MONTHLY,
        HOROSCOPE_TYPES.YEARLY,
        HOROSCOPE_TYPES.LOVE
      ];
    } else if (Object.values(HOROSCOPE_TYPES).includes(typeArg)) {
      // 如果指定了有效的类型，则只处理该类型
      types = [typeArg];
    } else {
      console.error(`无效的运势类型: ${typeArg}`);
      console.log(`有效的运势类型: ${Object.values(HOROSCOPE_TYPES).join(', ')} 或 all`);
      process.exit(1);
    }
    
    // 遍历所有需要处理的语言
    for (const language of languages) {
      
      // 遍历所有需要处理的运势类型
      for (const type of types) {
        await generateLatestThreeForType(type, language);
      }
      
    }
    
    printDivider('所有星座运势生成完成');
  } catch (error) {
    console.error('生成星座运势失败:', error);
    process.exit(1);
  }
}

// 执行主函数
main(); 