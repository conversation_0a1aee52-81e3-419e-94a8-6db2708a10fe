import React, { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useUser } from '../contexts/UserContext';
import LandingBackground from '../components/LandingBackground';
import Footer from '../components/Footer';
import SEO from '../components/SEO';
import { useLanguageNavigate } from '../hooks/useLanguageNavigate';
import LoginPrompt from '../components/LoginPrompt';
import VipPromptDialog from '../components/VipPromptDialog';
import { TAROT_CARDS } from '../data/tarot-cards';
import { useTarotProgress } from '../hooks/useTarotProgress';
import axiosInstance from '../utils/axios';
import { FortuneReadingModal } from '../components/daily-fortune';
import SingleCardReading from '../components/yes-no-tarot/SingleCardReading';
import { getFontClass as getGlobalFontClass } from '../utils/fontUtils';

// 导入其他组件
import SingleCardHowItWords from '../components/yes-no-tarot/SingleCardHowItWords';
import MoreTarotOptions from '../components/yes-no-tarot/MoreTarotOptions';
import SpotlightSection from '../components/yes-no-tarot/SingleCardSpotlightSection';
import SingleCardBenefits from '../components/yes-no-tarot/SingleCardBenefits';
import SingleCardBestQuestions from '../components/yes-no-tarot/SingleCardBestQuestions';
import SingleCardUnderstanding from '../components/yes-no-tarot/SingleCardUnderstanding';

const YesNoSingleCard: React.FC = () => {
  const { t, i18n } = useTranslation();
  const { user } = useUser();
  const { navigate } = useLanguageNavigate();
  const [showLoginPrompt, setShowLoginPrompt] = useState(false);
  const [showVipPrompt, setShowVipPrompt] = useState(false);
  const [showFortuneModal, setShowFortuneModal] = useState(false);
  
  // 进度条相关状态和hooks
  const {
    progress,
    startProgress,
    markApiReceived,
    completeProgress
  } = useTarotProgress({
    duration: 30000, // 30秒
    onComplete: () => {
      // 进度条完成后的回调
    }
  });
  const [readingResult, setReadingResult] = useState<any>(null);
  const [, setIsGeneratingReading] = useState(false);

  // 新增状态控制
  const [userQuestion, setUserQuestion] = useState(''); // 用户问题
  const [isSubmitting, setIsSubmitting] = useState(false); // 提交状态
  const [errorMessage, setErrorMessage] = useState(''); // 错误信息
  const [showCard, setShowCard] = useState(false); // 是否显示卡牌区域
  
  // 安全检测相关状态
  const [safetyIssue, setSafetyIssue] = useState(false); // 是否有安全问题
  const [safetyMessage, setSafetyMessage] = useState(''); // 安全检测回复模板内容
  
  // 卡牌相关状态
  const [cardBackImage] = useState<string>("https://cdn.tarotqa.com/public/home-images-001-sm.webp");
  const [flipped, setFlipped] = useState(false);
  const [processingCard, setProcessingCard] = useState(false);
  const [cardImage, setCardImage] = useState<string | null>(null);
  const [selectedCard, setSelectedCard] = useState<number | null>(null);
  const [cardOrientation, setCardOrientation] = useState(false); // false为正位，true为逆位

  // 创建适配器函数，将带参数的getFontClass转换为不带参数的版本
  const getFontClass = useCallback(() => {
    return getGlobalFontClass(i18n.language);
  }, [i18n.language]);

  // 检查用户权限
  const checkUserPermission = () => {
    if (!user) {
      setShowLoginPrompt(true);
      return false;
    }
    if (user.vipStatus === 'active') return true;
    if (user.remainingReads <= 0) {
      setShowVipPrompt(true);
      return false;
    }
    return true;
  };

  // 返回选择界面
  const handleBackToSelection = () => {
    navigate('/yes-no-tarot'); // 返回到选择页面
  };
  
  // 开始塔罗阅读 - 用于SpotlightCard组件
  const handleStartReading = () => {
    if (!checkUserPermission()) return;
    
    // 直接导航到当前页面，强制刷新
    navigate('/yes-no-tarot/single-card');
  };

  // 处理卡牌翻转
  const handleCardFlip = () => {
    if (processingCard) {
      return;
    }

    setProcessingCard(true);
    
    // 随机选择一张卡牌
    const randomCardIndex = Math.floor(Math.random() * TAROT_CARDS.length);
    setSelectedCard(randomCardIndex);
    
    // 随机确定卡牌正逆位（50%概率）
    const isReversed = Math.random() < 0.5;
    setCardOrientation(isReversed);
    
    // 设置为已翻开状态
    setFlipped(true);
    
    // 设置卡牌图片
    setCardImage(`/images-optimized/tarot/${TAROT_CARDS[randomCardIndex].nameEn}.webp`);
    
    // 卡片动画完成后，从处理中状态移除并开始解读
    setTimeout(() => {
      setProcessingCard(false);
      
      // 检查用户权限
      if (!checkUserPermission()) return;
      
      // 开始进度条动画并显示进度条模态框
      setShowFortuneModal(true);
      startProgress();
      setIsGeneratingReading(true);
      
      // 准备卡牌数据并调用API
      const card = TAROT_CARDS[randomCardIndex];
      const cardData = {
        name: card.name,
        nameEn: card.nameEn,
        isReversed: isReversed
      };
      
      // 调用API获取解读结果
      axiosInstance.post('/api/yes-no-tarot', {
        question: userQuestion,
        card: cardData,
        language: i18n.language
      })
      .then(response => {
        // 标记API响应已收到
        markApiReceived();
        
        // 检查是否存在安全问题
        if (response.data.ethicalIssue) {
          setSafetyIssue(true);
          setSafetyMessage(response.data.message || '您的问题可能涉及敏感内容，无法提供解读');
          
          // 完成进度条
          completeProgress(() => {
            setShowFortuneModal(false);
            setIsGeneratingReading(false);
          });
          return;
        }
        
        // 保存解读结果
        if (response.data && response.data.reading && response.data.reading.content) {
          setReadingResult(response.data.reading.content);
        } else {
          setReadingResult(response.data.reading);
        }
        
        // 完成进度条
        completeProgress(() => {
          setShowFortuneModal(false);
          setIsGeneratingReading(false);
        });
      })
      .catch(() => {
        completeProgress(() => {
          setShowFortuneModal(false);
          alert(t('yes_no_tarot.error.reading_failed', '很抱歉，生成解读时出现了问题，请稍后再试。'));
          setIsGeneratingReading(false);
        });
      });
    }, 1000);
  };

  // 验证问题
  const validateQuestion = (question: string): { isValid: boolean; errorMessage: string } => {
    const trimmedQuestion = question.trim();
    const currentLang = i18n.language;
    
    // 检查问题长度
    if (trimmedQuestion.length < 5) {
      return { isValid: false, errorMessage: t('home.validation.too_short') };
    }
    if (trimmedQuestion.length > 200) {
      return { isValid: false, errorMessage: t('home.validation.too_long') };
    }

    // 检查是否包含表情符号
    const emojiRegex = /[\u{1F000}-\u{1F6FF}\u{1F900}-\u{1F9FF}\u{2600}-\u{26FF}\u{2700}-\u{27BF}]/u;
    if (emojiRegex.test(trimmedQuestion)) {
      return { isValid: false, errorMessage: t('home.validation.contains_emoji') };
    }

    // 根据语言设置不同的验证规则
    if (currentLang === 'zh-CN' || currentLang === 'zh-TW') {
      // 中文验证规则
      if (!/[\u4e00-\u9fa5]/.test(trimmedQuestion)) {
        return { isValid: false, errorMessage: t('home.validation.invalid_question') };
      }
    } else if (currentLang === 'ja') {
      // 日文验证规则
      if (!/[\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF]/.test(trimmedQuestion)) {
        return { isValid: false, errorMessage: t('home.validation.invalid_question') };
      }
    } else {
      // 英文验证规则
      if (!/[a-zA-Z]/.test(trimmedQuestion)) {
        return { isValid: false, errorMessage: t('home.validation.invalid_question') };
      }
    }

    // 检查是否为纯数字
    if (/^\d+$/.test(trimmedQuestion)) {
      return { isValid: false, errorMessage: t('home.validation.invalid_question') };
    }

    // 检查重复字符
    if (/(.)\1{4,}/.test(trimmedQuestion)) {
      return { isValid: false, errorMessage: t('home.validation.invalid_question') };
    }

    // 检查测试文本 - 根据语言设置不同的测试词
    const testPatterns = {
      'zh-CN': ['测试', 'test', '123', 'abc'],
      'zh-TW': ['測試', 'test', '123', 'abc'],
      'ja': ['テスト', 'test', '123', 'abc'],
      'en': ['test', '123', 'abc']
    };

    if (testPatterns[currentLang as keyof typeof testPatterns].some(pattern => 
      trimmedQuestion.toLowerCase().includes(pattern.toLowerCase())
    )) {
      return { isValid: false, errorMessage: t('home.validation.invalid_question') };
    }

    return { isValid: true, errorMessage: '' };
  };

  // 提交问题处理函数
  const handleSubmitQuestion = (e: React.FormEvent) => {
    e.preventDefault();

    const validation = validateQuestion(userQuestion);
    if (!validation.isValid) {
      setErrorMessage(validation.errorMessage);
      return;
    }

    if (!userQuestion.trim()) return;
    setIsSubmitting(true);
    
    // 模拟提交处理
    setTimeout(() => {
      setIsSubmitting(false);
      // 显示卡牌区域
      setShowCard(true);
      
      // 删除滚动到卡牌区域的代码
    }, 1000);
  };

  return (
    <div className="main-container min-h-screen flex flex-col relative">
      <SEO 
      />
      <LandingBackground />
      
      {/* 主要内容 */}
      <div className="flex-grow relative z-10">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 pt-0 sm:pt-1 pb-8">
          <div className="text-center mb-4 sm:mb-6 mt-6 sm:mt-8 md:mt-10">
            <h1 className={`main-title mb-2 sm:mb-3 ${getGlobalFontClass(i18n.language)} dark:text-white text-gray-900`}>
              {t('yes_no_tarot.single_card_subtitle')}
            </h1>
            <p className={`text-base sm:text-lg dark:text-purple-300 text-purple-600 italic ${getGlobalFontClass(i18n.language)}`}>
              {t('yes_no_tarot.single_card_subtitle_description')}
            </p>
          </div>

          {/* 主页面内容 */}
          <div className="max-w-[95%] lg:max-w-5xl mx-auto mt-0 sm:mt-8">
            {/* 单卡占卜组件 */}
            <SingleCardReading
              userQuestion={userQuestion}
              setUserQuestion={setUserQuestion}
              errorMessage={errorMessage}
              isSubmitting={isSubmitting}
              showCard={showCard}
              flipped={flipped}
              cardBackImage={cardBackImage}
              selectedCard={selectedCard}
              cardImage={cardImage}
              cardOrientation={cardOrientation}
              processingCard={processingCard}
              readingResult={readingResult}
              safetyIssue={safetyIssue}
              safetyMessage={safetyMessage}
              getFontClass={getFontClass}
              onSubmitQuestion={handleSubmitQuestion}
              handleCardFlip={handleCardFlip}
              onBackToSelection={handleBackToSelection}
            />
            
            {/* Best Questions for One Card Tarot Yes/No 板块 */}
            <SingleCardBestQuestions />
            
            {/* How One Card Tarot Yes No Reading Works 板块 */}
            <SingleCardHowItWords />

            {/* Benefits of Single Card Yes No Tarot 板块 */}
            <SingleCardBenefits />

            {/* Understanding Your One Card Reading 板块 */}
            <SingleCardUnderstanding />

            {/* 更多塔罗占卜区域 */}
            <MoreTarotOptions onNavigate={navigate} pageType="single" />

            {/* SpotlightCard组件 */}
            <SpotlightSection onStartReading={handleStartReading} />
          </div>
        </div>
      </div>

      <Footer />

      {/* VIP提示弹窗 */}
      <VipPromptDialog isOpen={showVipPrompt} onCancel={() => setShowVipPrompt(false)} />
      
      {/* 登录提示弹窗 */}
      <LoginPrompt isOpen={showLoginPrompt} onClose={() => setShowLoginPrompt(false)} />
      
      {/* 运势解读弹窗 */}
      <FortuneReadingModal
        isOpen={showFortuneModal}
        progress={progress}
      />
    </div>
  );
};

export default YesNoSingleCard; 