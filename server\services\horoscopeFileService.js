const fs = require('fs');
const path = require('path');

// 星座ID列表
const ZODIAC_SIGNS = [
  'aries', 'taurus', 'gemini', 'cancer', 
  'leo', 'virgo', 'libra', 'scorpio', 
  'sagittarius', 'capricorn', 'aquarius', 'pisces'
];

// 运势类型
const HOROSCOPE_TYPES = {
  DAILY: 'daily',
  WEEKLY: 'weekly',
  MONTHLY: 'monthly',
  YEARLY: 'yearly',
  LOVE: 'love'
};

// 本地文件存储目录 - 固定使用生产环境路径
const HOROSCOPE_DIR = '/var/www/tarot/horoscopes';

/**
 * 格式化日期为YYYY-MM-DD格式
 * @param {Date} date 日期对象
 * @returns {string} 格式化后的日期字符串
 */
function formatDateForDb(date) {
  const d = new Date(date);
  // 使用UTC方法获取年月日，避免时区影响
  const year = d.getUTCFullYear();
  const month = String(d.getUTCMonth() + 1).padStart(2, '0');
  const day = String(d.getUTCDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

/**
 * 确保目录存在
 * @param {string} dirPath 目录路径
 */
function ensureDirectoryExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

/**
 * 获取特定类型的星座运势文件路径
 * @param {string} type 运势类型
 * @param {Date} date 日期
 * @param {string} language 语言
 * @returns {string} 文件路径
 */
function getHoroscopeFilePath(type, date, language = 'zh-CN') {
  const dateStr = formatDateForDb(date);
  const dirPath = path.join(HOROSCOPE_DIR, language, type);
  ensureDirectoryExists(dirPath);

  return path.join(dirPath, `${dateStr}.json`);
}

/**
 * 保存星座运势数据到本地文件
 * @param {string} type 运势类型
 * @param {Date} date 日期
 * @param {Object} data 星座运势数据
 * @param {string} language 语言
 * @param {string} rawContent API返回的原始文本
 * @returns {Promise<boolean>} 是否成功
 */
async function saveHoroscopeToFile(type, date, data, language = 'zh-CN', rawContent = '') {
  try {
    // 获取文件路径
    const filePath = getHoroscopeFilePath(type, date, language);
    
    // 添加时间戳和元数据
    const fileData = {
      type,
      date: formatDateForDb(date),
      language,
      timestamp: Date.now(),
      data,
      raw_content: rawContent // 保存API返回的原始文本
    };
    
    // 写入文件
    fs.writeFileSync(filePath, JSON.stringify(fileData, null, 2), 'utf8');
    console.log(`星座运势数据已保存到: ${filePath}`);
    
    // 清理旧文件，只保留最新的十个日期
    await cleanupOldFiles(type, language);
    
    return true;
  } catch (error) {
    console.error(`保存星座运势数据到文件失败: ${error.message}`);
    return false;
  }
}

/**
 * 从本地文件获取星座运势数据
 * @param {string} type 运势类型
 * @param {Date} date 日期
 * @param {string} language 语言
 * @returns {Promise<Object|null>} 星座运势数据或null
 */
async function getHoroscopeFromFile(type, date, language = 'zh-CN') {
  try {
    const filePath = getHoroscopeFilePath(type, date, language);
    
    if (!fs.existsSync(filePath)) {
    //   console.log(`星座运势文件不存在: ${filePath}`);
      return null;
    }
    
    // 读取文件
    const fileContent = fs.readFileSync(filePath, 'utf8');
    const fileData = JSON.parse(fileContent);
    
    // console.log(`成功从文件读取星座运势数据: ${filePath}`);
    return fileData;
  } catch (error) {
    console.error(`从文件获取星座运势数据失败: ${error.message}`);
    return null;
  }
}

/**
 * 获取特定星座的运势数据
 * @param {string} sign 星座ID
 * @param {string} type 运势类型
 * @param {Date} date 日期
 * @param {string} language 语言
 * @returns {Promise<Object|null>} 星座运势数据或null
 */
async function getSignHoroscopeFromFile(sign, type, date, language = 'zh-CN') {
  try {
    const fileData = await getHoroscopeFromFile(type, date, language);
    
    if (!fileData || !fileData.data || !fileData.data[sign]) {
      return null;
    }
    
    return {
      sign,
      type,
      date: fileData.date,
      content: fileData.data[sign],
      language,
      generated_at: new Date(fileData.timestamp)
    };
  } catch (error) {
    console.error(`获取星座 ${sign} 运势数据失败: ${error.message}`);
    return null;
  }
}

/**
 * 获取所有星座的运势数据
 * @param {string} type 运势类型
 * @param {Date} date 日期
 * @param {string} language 语言
 * @returns {Promise<Object|null>} 所有星座运势数据或null
 */
async function getAllHoroscopesFromFile(type, date, language = 'zh-CN') {
  try {
    const fileData = await getHoroscopeFromFile(type, date, language);
    
    if (!fileData || !fileData.data) {
      return null;
    }
    
    // 转换为以星座ID为键的对象
    const results = {};
    let foundCount = 0;
    
    ZODIAC_SIGNS.forEach(sign => {
      if (fileData.data[sign]) {
        results[sign] = {
          sign,
          type,
          date: fileData.date,
          content: fileData.data[sign],
          language,
          generated_at: new Date(fileData.timestamp)
        };
        foundCount++;
      }
    });
    
    if (foundCount === 0) {
    //   console.log(`文件中未找到任何星座数据`);
      return null;
    }
    
    // console.log(`从文件中找到 ${foundCount} 个星座数据`);
    return results;
  } catch (error) {
    console.error(`获取所有星座运势数据失败: ${error.message}`);
    return null;
  }
}

/**
 * 清理旧文件，只保留最新的十个日期
 * @param {string} type 运势类型
 * @param {string} language 语言
 */
async function cleanupOldFiles(type, language = 'zh-CN') {
  try {
    // 清理目录
    const dirPath = path.join(HOROSCOPE_DIR, language, type);
    await cleanupDirOldFiles(dirPath);
  } catch (error) {
    console.error(`清理旧文件失败: ${error.message}`);
  }
}

/**
 * 清理指定目录中的旧文件，只保留最新的十个
 * @param {string} dirPath 目录路径
 */
async function cleanupDirOldFiles(dirPath) {
  if (!fs.existsSync(dirPath)) {
    return;
  }
  
  // 获取目录中的所有文件
  const files = fs.readdirSync(dirPath)
    .filter(file => file.endsWith('.json'))
    .map(file => ({
      name: file,
      path: path.join(dirPath, file),
      date: file.replace('.json', '')
    }))
    .sort((a, b) => {
      // 按日期降序排序
      return new Date(b.date) - new Date(a.date);
    });
  
  // 保留最新的十个文件
  if (files.length > 10) {
    for (let i = 10; i < files.length; i++) {
      fs.unlinkSync(files[i].path);
      // console.log(`已删除旧的星座运势文件: ${files[i].path}`);
    }
  }
}

module.exports = {
  saveHoroscopeToFile,
  getHoroscopeFromFile,
  getSignHoroscopeFromFile,
  getAllHoroscopesFromFile,
  formatDateForDb,
  cleanupOldFiles,
  cleanupDirOldFiles
}; 