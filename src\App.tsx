import { useEffect, useState,lazy } from 'react';
import { BrowserRouter as Router, useLocation, Routes, Route } from 'react-router-dom';
import { HelmetProvider } from 'react-helmet-async';
import AppRoutes from './routes';
import Navbar from './components/Navbar';
import AnnouncementModal from './components/AnnouncementModal';
import { UserProvider } from './contexts/UserContext';
import { v4 as uuidv4 } from 'uuid';
import { applyScrollBehaviorFix, isMobileDevice } from './utils/preventOverscroll';

import { GoogleOAuthProvider } from '@react-oauth/google';
import i18n from 'i18next';
import { DropdownProvider } from './contexts/DropdownContext';
import { ThemeProvider } from './contexts/ThemeContext';
import { Toaster } from'react-hot-toast';
import './index.css';
import './styles/fonts.css';
import './styles/globals.css';
import './styles/NavbarStyles.css';
// import { useTranslation } from 'react-i18next';


const PaymentSuccess = lazy(() => import('./pages/PaymentSuccess'));
const PaymentError = lazy(() => import('./pages/PaymentError'));
const ScrollToTop = lazy(() => import('./components/ScrollToTop'));
const TarotResultPage = lazy(() => import('./pages/TarotResultPage'));

// I18n Provider Component
function I18nProvider({ children }: { children: React.ReactNode }) {
  const [isI18nInitialized, setIsI18nInitialized] = useState(false);

  useEffect(() => {
    // 检查i18n是否已经初始化
    if (i18n.isInitialized) {
      // 确保语言设置正确
      const savedLang = localStorage.getItem('i18nextLng');
      if (savedLang && i18n.language !== savedLang) {
        i18n.changeLanguage(savedLang).then(() => {
          setIsI18nInitialized(true);
        });
      } else {
        setIsI18nInitialized(true);
      }
    } else {
      // 即使未初始化也设置为true，使用默认语言
      setIsI18nInitialized(true);
    }
  }, []);

  if (!isI18nInitialized) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-black">
        <div className="text-white text-lg">Loading...</div>
      </div>
    );
  }

  return <>{children}</>;
}

// 由于 Router 和 location hooks 的限制，需要将页面访问记录逻辑移到单独的组件中
function PageViewTracker() {
  const location = useLocation();

  // 处理可能过长的URL路径
  const sanitizePath = (path: string) => {
    if (!path) return '';
    // 如果路径过长，截取前200个字符
    if (path.length > 200) {
      return path.substring(0, 200);
    }
    return path;
  };

  useEffect(() => {
    // 在组件挂载时生成或获取会话ID
    const storedSessionId = localStorage.getItem('browser_session_id');
    const sessionId = storedSessionId || uuidv4();
    
    if (!storedSessionId) {
      localStorage.setItem('browser_session_id', sessionId);
    }

    // 记录页面访问
    const recordPageView = async () => {
      try {
        const userId = localStorage.getItem('userId'); // 如果用户已登录
        const sanitizedPath = sanitizePath(location.pathname);
        const sanitizedReferrer = sanitizePath(document.referrer);
        
        // 确保sessionId不为空，使用默认值作为备选
        const currentSessionId = sessionId || uuidv4();
        
        await fetch(`${import.meta.env.VITE_API_URL}/api/stats/pageview`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            userId: userId || undefined,  // 如果 userId 为 null，则发送 undefined
            sessionId: currentSessionId,
            pagePath: sanitizedPath,
            referrer: sanitizedReferrer
          })
        });
      } catch (error) {
      }
    };

    recordPageView();
  }, [location.pathname]); // 当路径变化时记录新的页面访问

  return null;
}

// 需要 Google OAuth 的路由组件
function AuthenticatedRoutes() {
  return (
    <GoogleOAuthProvider clientId={import.meta.env.VITE_GOOGLE_CLIENT_ID}>
      <Routes>
        <Route path="/payment/success" element={<PaymentSuccess />} />
        <Route path="/payment/error" element={<PaymentError />} />
        <Route path="/tarot-result" element={<TarotResultPage />} />
        <Route path="/*" element={<AppRoutes />} />
      </Routes>
    </GoogleOAuthProvider>
  );
}

// 创建一个新的组件来处理主要内容
function MainContent() {
  const location = useLocation();
  
  // 常用页面路径数组，用于简化条件判断
  const commonPaths = [
    'home', 'terms', 'privacy', 'yearly-fortune', 'yearly-fortune-result', 'user-card', 'tarot-result', 
    'tarot-gallery', 'gallery', 'spread-selection', 'spreads', 'reader-selection', 
    'membership', 'membership-inter', 'history', 'feedback', 'card-back-settings',
    'daily-fortune', 'daily-fortune-result', 'reading/reader', 'reading/spread', 'login', 'register', 'forgot-password',
    'reset-password', 'reading/shuffle', 'blog', 'yes-no-tarot', 'yes-no-tarot/single-card', 'yes-no-tarot/three-cards',
    'horoscope', 'horoscope/selection', 'horoscope/daily-horoscope', 'horoscope/weekly-horoscope', 'horoscope/monthly-horoscope', 'horoscope/yearly-horoscope', 'horoscope/love-horoscope',
    'tarot-guide', 'zodiac-traits', 'general-divination'
  ];
  
  // 检查是否是需要特殊处理的页面路径
  const isHomePath = location.pathname === '/' || 
                    commonPaths.some(path => location.pathname === `/${path}`) ||
                    commonPaths.some(path => location.pathname.match(new RegExp(`^/[^/]+/${path}$`))) ||
                    location.pathname.match(/^\/history\/[^\/]+$/) || // 添加对history详情页的支持
                    location.pathname.match(/^\/[^\/]+\/history\/[^\/]+$/) || // 添加对带语言参数的history详情页的支持
                    location.pathname.match(/^\/horoscope\/[^\/]+\/[^\/]+(?:\/[^\/]+)?$/) || // 添加对星座详情页的支持
                    location.pathname.match(/^\/[^\/]+\/horoscope\/[^\/]+\/[^\/]+(?:\/[^\/]+)?$/) || // 添加对带语言参数的星座详情页的支持
                    location.pathname.match(/^\/horoscope\/[^\/]+-[^\/]+-horoscope$/) || // 添加对统一星座详情页的支持
                    location.pathname.match(/^\/[^\/]+\/horoscope\/[^\/]+-[^\/]+-horoscope$/) || // 添加对带语言参数的统一星座详情页的支持
                    location.pathname.match(/^\/blog\/horoscope\/[^\/]+\/[^\/]+(?:\/[^\/]+)?$/) || // 添加对星座运势详情页的支持
                    location.pathname.match(/^\/[^\/]+\/blog\/horoscope\/[^\/]+\/[^\/]+(?:\/[^\/]+)?$/) || // 添加对带语言参数的星座运势详情页的支持
                    location.pathname.match(/^\/tarot-guide$/) || // 添加对新路径的支持
                    location.pathname.match(/^\/[^\/]+\/tarot-guide$/) || // 添加对带语言参数的新路径的支持
                    location.pathname.match(/^\/zodiac-traits$/) || // 添加对新路径的支持
                    location.pathname.match(/^\/[^\/]+\/zodiac-traits$/) || // 添加对带语言参数的新路径的支持
                    location.pathname.match(/^\/general-divination$/) || // 添加对大众占卜路径的支持
                    location.pathname.match(/^\/[^\/]+\/general-divination$/) || // 添加对带语言参数的大众占卜路径的支持
                    location.pathname.match(/^\/[^\/]+\/?$/) || // 带语言参数的根路径，如 /zh-CN/
                    location.pathname.match(/^\/[^\/]+$/); // 带语言参数的根路径，如 /zh-CN
  
  return (
    <>
      {/* NewsAlert组件已被禁用 */}
      <main className={`flex-1 relative z-10 overflow-auto main-content loaded ${isHomePath ? '' : 'pt-24'}`}>
      <Routes>
        <Route path="/*" element={<AuthenticatedRoutes />} />
      </Routes>
      <Toaster 
        position="top-center"
        gutter={20}
        containerStyle={{
          top: 80,
          left: '50%',
          transform: 'translateX(-50%)',
          width: '100%',
          maxWidth: '600px',
        }}
        toastOptions={{
          className: 'bg-mystic-800 text-white',
          duration: 3000,
          style: {
            maxWidth: '90%',
            margin: '0 auto',
          },
        }}
      />
    </main>
    </>
  );
}

function App() {
  useEffect(() => {
    // 根据设备类型应用相应的滚动优化
    applyScrollBehaviorFix();
    
    // 确保PC端滚动条显示正常
    if (!isMobileDevice()) {
      document.body.style.overflow = 'auto';
      document.documentElement.style.overflow = 'auto';
      
      // 确保PC端滚动条可见
      const styleTag = document.createElement('style');
      styleTag.innerHTML = `
        html, body, #root, .main-content {
          overflow: auto !important;
        }
        ::-webkit-scrollbar {
          display: block !important;
        }
      `;
      document.head.appendChild(styleTag);
      
      return () => {
        document.body.style.backgroundColor = '';
        // 移除临时样式
        document.head.removeChild(styleTag);
      };
    }
    
    // 移动设备处理
    return () => {
      document.body.style.backgroundColor = '';
    };
  }, []); // 只在组件首次加载时执行

  const handleCloseAnnouncement = () => {
    // 在localStorage中存储用户已关闭公告的信息
    localStorage.setItem('announcement_closed', 'true');
  };

  return (
    <HelmetProvider>
      <I18nProvider>
        <ThemeProvider>
          <DropdownProvider>
            <Router>
              <UserProvider>
                <div className="app min-h-screen flex flex-col relative bg-background text-foreground">
                  <style>
                    {`
                      body {
                        background-color: hsl(var(--background));
                        color: hsl(var(--foreground));
                        transition: background-color 0.3s ease, color 0.3s ease;
                      }
                      .dark body {
                        background-color: #000000; 
                      }
                      body:not(.dark) {
                        background-color: #ffffff;
                        color: #000000;
                      }
                      .app {
                        transition: opacity 0.3s ease-in-out;
                      }
                      .app:not(.dark) {
                        color: #000000;
                      }
                      /* 浅色主题下主标题样式 */
                      body:not(.dark) .main-title {
                        background: none !important;
                        -webkit-background-clip: initial !important;
                        -webkit-text-fill-color: #000 !important;
                        background-clip: initial !important;
                        color: #000 !important;
                      }
                      .page-transition-enter {
                        opacity: 0;
                      }
                      .page-transition-enter-active {
                        opacity: 1;
                      }
                      .page-transition-exit {
                        opacity: 1;
                      }
                      .page-transition-exit-active {
                        opacity: 0;
                      }
                      /* 添加全局过渡效果 */
                      .main-content {
                        transition: opacity 0.3s ease-in-out;
                      }
                      .main-content.loading {
                        opacity: 0;
                      }
                      .main-content.loaded {
                        opacity: 1;
                      }
                    `}
                  </style>
                  <Navbar className="z-50" />
                  <PageViewTracker />
                  <ScrollToTop />
                  <MainContent />
                  <AnnouncementModal onClose={handleCloseAnnouncement} />
                </div>
              </UserProvider>
            </Router>
          </DropdownProvider>
        </ThemeProvider>
      </I18nProvider>
    </HelmetProvider>
  );
}

export default App;